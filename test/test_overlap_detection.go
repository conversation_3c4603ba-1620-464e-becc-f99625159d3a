package main

import (
	"log"

	"program-manager/config"
	"program-manager/diurnal"
)

func main() {
	log.Println("=== Diurnal Overlap Detection Test ===")

	// Load the test configuration
	diurnalConfig, err := config.LoadDiurnalConfig("configs/diurnalConfigOverlapTest.json")
	if err != nil {
		log.Fatalf("Failed to load test configuration: %v", err)
	}

	log.Printf("Loaded configuration with %d schedules", len(diurnalConfig.Schedules))

	// Print original period statuses
	log.Println("\n--- Original Period Statuses ---")
	for _, schedule := range diurnalConfig.Schedules {
		log.Printf("Schedule %s (%s):", schedule.ScheduleId, schedule.ProgramName)
		for _, period := range schedule.Periods {
			log.Printf("  Period %s (%s): %s - %s [STATUS: %s]",
				period.PeriodId, period.Name, period.StartTime, period.EndTime, period.PeriodStatus)
		}
	}

	// Convert to static times first
	log.Println("\n--- Converting to Static Times ---")
	staticConfig, err := diurnal.ConvertRelativeTimesToStatic(&diurnalConfig, nil)
	if err != nil {
		log.Fatalf("Failed to convert to static times: %v", err)
	}

	// Run overlap detection
	log.Println("\n--- Running Overlap Detection ---")
	diurnal.DetectAndHandleOverlaps(staticConfig)

	// Print updated period statuses
	log.Println("\n--- Updated Period Statuses ---")
	for _, instance := range staticConfig.Instances {
		log.Printf("Instance %s (%s):", instance.InstanceId, instance.ProgramName)
		for _, period := range instance.Periods {
			status := period.PeriodStatus
			if status == "notUsedOverlap" {
				status = status + " ⚠️"
			}
			log.Printf("  Period %s (%s): %s - %s [STATUS: %s]",
				period.PeriodId, period.Name, period.StartTime, period.EndTime, status)
		}
	}

	// Print overlap status summary
	diurnal.PrintPeriodOverlapStatus(*staticConfig)

	log.Println("\n=== Test Complete ===")
	log.Println("Expected result: 'earlyAfternoon' period should be marked as 'notUsedOverlap'")
	log.Println("because it starts only 20 minutes after the 'morning' period ends (< 30 min threshold)")
}
