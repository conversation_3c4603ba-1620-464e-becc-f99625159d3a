package main

import (
	"log"

	"program-manager/config"
	"program-manager/diurnal"
	"program-manager/types"
)

func main() {
	log.Println("=== Diurnal Relative Time Overlap Detection Test ===")

	// Load the test configuration
	diurnalConfig, err := config.LoadDiurnalConfig("configs/diurnalConfigRelativeOverlapTest.json")
	if err != nil {
		log.Fatalf("Failed to load test configuration: %v", err)
	}

	log.Printf("Loaded configuration with %d instances", len(diurnalConfig.Instances))

	// Create a mock clock state for testing
	clockState := &types.ClockState{
		Dawn: "06:30",
		Dusk: "18:45",
	}

	// Print original period statuses
	log.Println("\n--- Original Period Statuses ---")
	for _, instance := range diurnalConfig.Instances {
		log.Printf("Instance %s (%s):", instance.InstanceId, instance.ProgramName)
		for _, period := range instance.Periods {
			log.Printf("  Period %s (%s): %s - %s [STATUS: %s]",
				period.PeriodId, period.Name, period.StartTime, period.EndTime, period.PeriodStatus)
		}
	}

	log.Printf("\nUsing clock state: Dawn=%s, Dusk=%s", clockState.Dawn, clockState.Dusk)
	log.Println("Expected resolved times:")
	log.Println("  earlyMorning: 07:30 - 09:30 (01:00 afterDawn - 03:00 afterDawn)")
	log.Println("  midMorning: 09:45 - 11:30 (03:15 afterDawn - 05:00 afterDawn)")
	log.Println("  Gap between periods: 15 minutes (< 30 min threshold)")

	// Convert to static times first
	log.Println("\n--- Converting to Static Times ---")
	staticConfig, err := diurnal.ConvertRelativeTimesToStatic(&diurnalConfig, clockState)
	if err != nil {
		log.Fatalf("Failed to convert to static times: %v", err)
	}

	// Run overlap detection
	log.Println("\n--- Running Overlap Detection ---")
	diurnal.DetectAndHandleOverlaps(staticConfig)

	// Print updated period statuses
	log.Println("\n--- Updated Period Statuses ---")
	for _, instance := range staticConfig.Instances {
		log.Printf("Instance %s (%s):", instance.InstanceId, instance.ProgramName)
		for _, period := range instance.Periods {
			status := period.PeriodStatus
			if status == "notUsedOverlap" {
				status = status + " ⚠️"
			}
			log.Printf("  Period %s (%s): %s - %s [STATUS: %s]",
				period.PeriodId, period.Name, period.StartTime, period.EndTime, status)
		}
	}

	// Print overlap status summary
	diurnal.PrintPeriodOverlapStatus(*staticConfig)

	log.Println("\n=== Test Complete ===")
	log.Println("Expected result: 'midMorning' period should be marked as 'notUsedOverlap'")
	log.Println("because it starts only 15 minutes after the 'earlyMorning' period ends (< 30 min threshold)")
}
