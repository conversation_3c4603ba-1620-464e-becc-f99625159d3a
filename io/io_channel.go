package io

import (
	"program-manager/shared"
	"fmt"
)

// IOChannelImpl implements AggregatorToIOChannel
type IOChannelImpl struct {
	readRequestChan  chan interface{}
	writeRequestChan chan shared.WriteRequest
	closeChan        chan bool
	ioModule         *IOModuleImpl
}

// Send sends generic data to IO
func (ch *IOChannelImpl) Send(data interface{}) error {
	select {
	case ch.readRequestChan <- data:
		return nil
	default:
		return fmt.<PERSON><PERSON>rf("IO channel is full")
	}
}

// Receive receives data from IO (not used in this direction)
func (ch *IOChannelImpl) Receive() (interface{}, error) {
	return nil, fmt.Errorf("receive not supported on AggregatorToIOChannel")
}

// Close closes the IO channel
func (ch *IOChannelImpl) Close() error {
	close(ch.readRequest<PERSON>han)
	close(ch.writeRequestChan)
	return nil
}

// SendReadRequest sends read request to firmware
func (ch *IOChannelImpl) SendReadRequest(req interface{}) error {
	select {
	case ch.readRequest<PERSON>han <- req:
		return nil
	default:
		return fmt.Errorf("IO read request channel is full")
	}
}

// SendWriteRequest sends write request to firmware
func (ch *IOChannelImpl) SendWriteRequest(req shared.WriteRequest) error {
	select {
	case ch.writeRequestChan <- req:
		return nil
	default:
		return fmt.Errorf("IO write request channel is full")
	}
}
