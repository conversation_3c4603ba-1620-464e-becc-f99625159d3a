package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"program-manager/diurnal"
	"program-manager/redis"
	"program-manager/types"
)

// getClockStateFromDiurnal gets the clock state from the diurnal package
func getClockStateFromDiurnal() *types.ClockState {
	// Use the diurnal package's global clock state
	return diurnal.GetGlobalClockState()
}

// CalculateCurrentSetpoint calculates the current required setpoint value based on ramp rate
func CalculateCurrentSetpoint(startSetpoint float64, rampRate float64, minutesElapsed float64) float64 {
	return startSetpoint + (rampRate * minutesElapsed)
}

// MonitorRampRates continuously monitors and prints required setpoint values using state-based logic
func MonitorRampRates(ctx context.Context, config types.DiurnalSetpointConfig, programManager map[string]interface{}) {
	// Check if diurnal program is enabled
	if _, ok := programManager["diurnalSetpoint"].(types.DiurnalSetpointConfig); !ok {
		log.Println("Diurnal program is not enabled, skipping ramp rate monitoring")
		return
	}

	// Try to get clock state for relative time calculations
	if clockConfig, clockEnabled := programManager["clock"].(types.ClockConfig); clockEnabled && clockConfig.Enabled {
		// We'll get the clock state from Redis or use the diurnal package's global state
		log.Println("Clock is enabled, monitoring will use real dawn/dusk times for relative calculations")
	} else {
		log.Println("Clock not enabled, monitoring will use default dawn/dusk times")
	}

	log.Println("Starting intelligent diurnal monitoring...")
	for {
		select {
		case <-ctx.Done():
			log.Println("Diurnal monitoring stopped")
			return
		default:
			now := time.Now()
			log.Printf("\n🕐 Diurnal Status at %s", now.Format("15:04:05"))

			for _, instance := range config.Instances {
				if !instance.Enabled {
					log.Printf("   Instance %s: ⏸️  Disabled", instance.InstanceId)
					continue
				}

				// Get current state using improved logic
				diurnalState := diurnal.GetDiurnalState(now, instance)

				switch diurnalState.State {
				case types.StateInPeriod:
					log.Printf("   Instance %s: 🎯 In Period %s (%s)",
						instance.InstanceId, diurnalState.CurrentPeriod.PeriodId, diurnalState.CurrentPeriod.Name)
					for name, value := range diurnalState.CurrentValues {
						log.Printf("      %s: %.2f", name, value)
					}

				case types.StateRamping:
					log.Printf("   Instance %s: 📈 Ramping from Period %s to %s (%.1f min elapsed)",
						instance.InstanceId, diurnalState.CurrentPeriod.PeriodId, diurnalState.NextPeriod.PeriodId, diurnalState.MinutesElapsed)

					// Show ramp rates and current values with aligned formatting
					for name, currentValue := range diurnalState.CurrentValues {
						startValue := diurnalState.CurrentPeriod.Setpoints[name]
						targetValue := diurnalState.NextPeriod.Setpoints[name]
						rampRate, _ := diurnal.CalculateRampRate(*diurnalState.CurrentPeriod, *diurnalState.NextPeriod, name)

						log.Printf("      %-17s %.2f → %.2f   (current: %.2f, rate: %.3f/min)",
							name+":", startValue, targetValue, currentValue, rampRate)
					}

				case types.StateIdle:
					log.Printf("   Instance %s: 💤 Idle (no active periods)", instance.InstanceId)

					// Show next upcoming period if any
					nextPeriod := findNextPeriod(now, instance)
					if nextPeriod != nil {
						timeUntilNext := calculateTimeUntilPeriod(now, *nextPeriod)
						log.Printf("      Next: Period %s (%s) in %s",
							nextPeriod.PeriodId, nextPeriod.Name, formatDuration(timeUntilNext))
					}

				default:
					log.Printf("   Instance %s: ❓ Unknown state", instance.InstanceId)
				}

				// Store current state in Redis for other systems to use
				storeCurrentState(ctx, diurnalState, instance)
			}

			// Sleep for 5 seconds before next check
			time.Sleep(5 * time.Second)
		}
	}
}

// findNextPeriod finds the next upcoming period for an instance
func findNextPeriod(currentTime time.Time, instance types.DiurnalInstance) *types.Period {
	if !instance.Enabled {
		return nil
	}

	// Get dawn and dusk times for relative time resolution
	var dawnTime, duskTime time.Time
	clockState := getClockStateFromDiurnal()
	now := currentTime

	if clockState != nil {
		// Parse dawn and dusk times from clock state
		if dawn, err := time.Parse("15:04:05", clockState.Dawn); err == nil {
			dawnTime = time.Date(now.Year(), now.Month(), now.Day(), dawn.Hour(), dawn.Minute(), 0, 0, now.Location())
		} else if dawn, err := time.Parse("15:04", clockState.Dawn); err == nil {
			dawnTime = time.Date(now.Year(), now.Month(), now.Day(), dawn.Hour(), dawn.Minute(), 0, 0, now.Location())
		} else {
			dawnTime = time.Date(now.Year(), now.Month(), now.Day(), 6, 30, 0, 0, now.Location())
		}

		if dusk, err := time.Parse("15:04:05", clockState.Dusk); err == nil {
			duskTime = time.Date(now.Year(), now.Month(), now.Day(), dusk.Hour(), dusk.Minute(), 0, 0, now.Location())
		} else if dusk, err := time.Parse("15:04", clockState.Dusk); err == nil {
			duskTime = time.Date(now.Year(), now.Month(), now.Day(), dusk.Hour(), dusk.Minute(), 0, 0, now.Location())
		} else {
			duskTime = time.Date(now.Year(), now.Month(), now.Day(), 18, 45, 0, 0, now.Location())
		}
	} else {
		// Use default dawn/dusk times
		dawnTime = time.Date(now.Year(), now.Month(), now.Day(), 6, 30, 0, 0, now.Location())
		duskTime = time.Date(now.Year(), now.Month(), now.Day(), 18, 45, 0, 0, now.Location())
	}

	currentTimeOfDay := time.Date(0, 1, 1, currentTime.Hour(), currentTime.Minute(), 0, 0, time.UTC)
	var nextPeriod *types.Period
	var shortestWait time.Duration = 24 * time.Hour

	// Check today's periods
	dayName := strings.ToLower(currentTime.Weekday().String())
	for i := range instance.Periods {
		period := &instance.Periods[i]
		if !period.Enabled || !period.ActiveDays[dayName] {
			continue
		}

		// Resolve relative time to absolute time
		resolvedStartTime, err := resolveRelativeTimeForMonitoring(period.StartTime, dawnTime, duskTime)
		if err != nil {
			continue
		}

		startTime, err := time.Parse("15:04", resolvedStartTime)
		if err != nil {
			continue
		}
		startTimeOfDay := time.Date(0, 1, 1, startTime.Hour(), startTime.Minute(), 0, 0, time.UTC)

		if startTimeOfDay.After(currentTimeOfDay) {
			waitTime := startTimeOfDay.Sub(currentTimeOfDay)
			if waitTime < shortestWait {
				shortestWait = waitTime
				nextPeriod = period
			}
		}
	}

	// If no period found today, check tomorrow (simplified - just check next day)
	if nextPeriod == nil {
		tomorrow := currentTime.AddDate(0, 0, 1)
		tomorrowDayName := strings.ToLower(tomorrow.Weekday().String())

		for i := range instance.Periods {
			period := &instance.Periods[i]
			if period.Enabled && period.ActiveDays[tomorrowDayName] {
				nextPeriod = period
				break // Return first period of tomorrow
			}
		}
	}

	return nextPeriod
}

// resolveRelativeTimeForMonitoring converts relative time to absolute time using dawn/dusk
func resolveRelativeTimeForMonitoring(timeStr string, dawnTime, duskTime time.Time) (string, error) {
	// Check if it's a relative time
	if !strings.Contains(timeStr, "beforeDawn") && !strings.Contains(timeStr, "afterDawn") &&
		!strings.Contains(timeStr, "beforeDusk") && !strings.Contains(timeStr, "afterDusk") {
		return timeStr, nil // Return as-is if not relative
	}

	// Parse the time and reference
	parts := strings.Fields(timeStr)
	if len(parts) != 2 {
		return "", fmt.Errorf("invalid relative time format: %s", timeStr)
	}

	timePart := parts[0]
	reference := parts[1]

	// Parse the time offset
	offsetTime, err := time.Parse("15:04", timePart)
	if err != nil {
		return "", fmt.Errorf("invalid time in relative format: %v", err)
	}

	offset := time.Duration(offsetTime.Hour())*time.Hour + time.Duration(offsetTime.Minute())*time.Minute

	var baseTime time.Time
	switch reference {
	case "beforeDawn":
		baseTime = dawnTime.Add(-offset)
	case "afterDawn":
		baseTime = dawnTime.Add(offset)
	case "beforeDusk":
		baseTime = duskTime.Add(-offset)
	case "afterDusk":
		baseTime = duskTime.Add(offset)
	default:
		return "", fmt.Errorf("invalid time reference: %s", reference)
	}

	return baseTime.Format("15:04"), nil
}

// calculateTimeUntilPeriod calculates time until a period starts
func calculateTimeUntilPeriod(currentTime time.Time, period types.Period) time.Duration {
	// Get dawn and dusk times from clock state or use defaults
	var dawnTime, duskTime time.Time
	clockState := getClockStateFromDiurnal()
	now := currentTime

	if clockState != nil {
		// Parse dawn and dusk times from clock state (try both HH:MM:SS and HH:MM formats)
		if dawn, err := time.Parse("15:04:05", clockState.Dawn); err == nil {
			dawnTime = time.Date(now.Year(), now.Month(), now.Day(), dawn.Hour(), dawn.Minute(), 0, 0, now.Location())
		} else if dawn, err := time.Parse("15:04", clockState.Dawn); err == nil {
			dawnTime = time.Date(now.Year(), now.Month(), now.Day(), dawn.Hour(), dawn.Minute(), 0, 0, now.Location())
		} else {
			dawnTime = time.Date(now.Year(), now.Month(), now.Day(), 6, 30, 0, 0, now.Location())
		}

		if dusk, err := time.Parse("15:04:05", clockState.Dusk); err == nil {
			duskTime = time.Date(now.Year(), now.Month(), now.Day(), dusk.Hour(), dusk.Minute(), 0, 0, now.Location())
		} else if dusk, err := time.Parse("15:04", clockState.Dusk); err == nil {
			duskTime = time.Date(now.Year(), now.Month(), now.Day(), dusk.Hour(), dusk.Minute(), 0, 0, now.Location())
		} else {
			duskTime = time.Date(now.Year(), now.Month(), now.Day(), 18, 45, 0, 0, now.Location())
		}
	} else {
		// Use default dawn/dusk times
		dawnTime = time.Date(now.Year(), now.Month(), now.Day(), 6, 30, 0, 0, now.Location())
		duskTime = time.Date(now.Year(), now.Month(), now.Day(), 18, 45, 0, 0, now.Location())
	}

	// Resolve relative time to absolute time
	resolvedStartTime, err := resolveRelativeTimeForMonitoring(period.StartTime, dawnTime, duskTime)
	if err != nil {
		return 0
	}

	startTime, err := time.Parse("15:04", resolvedStartTime)
	if err != nil {
		return 0
	}

	// Calculate for today first
	startDateTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(),
		startTime.Hour(), startTime.Minute(), 0, 0, currentTime.Location())

	if startDateTime.After(currentTime) {
		return startDateTime.Sub(currentTime)
	}

	// If period is tomorrow, add 24 hours
	return startDateTime.Add(24 * time.Hour).Sub(currentTime)
}

// formatDuration formats a duration in a human-readable way
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0fs", d.Seconds())
	}
	if d < time.Hour {
		return fmt.Sprintf("%.0fm", d.Minutes())
	}
	hours := int(d.Hours())
	minutes := int(d.Minutes()) % 60
	return fmt.Sprintf("%dh%dm", hours, minutes)
}

// storeCurrentState stores the current diurnal state in Redis using modern format
func storeCurrentState(ctx context.Context, state types.DiurnalState, instance types.DiurnalInstance) {
	// Store state in modern format: hub:{hubId}:zone:{zoneId}:instance:{instanceId}:state
	key := fmt.Sprintf("hub:%s:zone:%s:instance:%s:state", instance.HubId, instance.ZoneId, instance.InstanceId)

	// Convert state to JSON
	stateJSON, err := json.Marshal(state)
	if err != nil {
		log.Printf("Warning: Failed to marshal diurnal state for instance %s: %v", state.InstanceID, err)
		return
	}

	// Store in Redis with 60-minute TTL
	err = redis.SetString(ctx, key, string(stateJSON))
	if err != nil {
		log.Printf("Warning: Failed to store diurnal state for instance %s: %v", state.InstanceID, err)
	}

	// Store individual setpoint values in CEB-compatible format only
	for name, value := range state.CurrentValues {
		// CEB-compatible format: hub:{hubId}:zone:{zoneId}:instance:{instanceId}:setpoint:{setpointName}
		cebKey := fmt.Sprintf("hub:%s:zone:%s:instance:%s:setpoint:%s",
			instance.HubId, instance.ZoneId, instance.InstanceId, name)
		err = redis.SetFloat(ctx, cebKey, value)
		if err != nil {
			log.Printf("Warning: Failed to store CEB setpoint %s for instance %s: %v", name, state.InstanceID, err)
		} else {
			// !NOTE: uncomment this to see setpoint updates in real-time
			// log.Printf("Monitoring: Updated setpoint %s = %.2f in Redis key: %s (TTL: 60 min)",
			// 	name, value, cebKey)
		}
	}
}
