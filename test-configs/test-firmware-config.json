{"zoneName": "zoneA", "deviceId": "1234567890", "mqttConf": {"host": "127.0.0.1", "port": 1883, "username": "admin", "password": "admin"}, "mqttTopics": {"readReqTopic": "readData/req", "readResTopic": "readData/res", "writeReqTopic": "writeData/req", "writeResTopic": "writeData/res", "periodicResTopic": "readData/periodic/res", "periodicityInterval": 5000}, "retry": {"count": 3, "waitTime": 5000}, "ports": [{"baudRate": 115200, "parity": "NONE", "cards": [{"cardId": "CD1", "modbusAddr": "0x23", "channels": [{"channelId": "CH1", "channelName": "Unit Heater", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "<PERSON><PERSON>", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH4", "channelName": "Temperature", "type": "input", "signalType": "analog", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH5", "channelName": "<PERSON><PERSON><PERSON><PERSON>", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}]}]}]}