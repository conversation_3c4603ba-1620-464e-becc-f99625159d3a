{"mqtt": {"broker": "paradox-dev-broker.1iot.io", "port": 8885, "username": "platform_user", "password": "ag439rdrjiz2h2dmbw2z", "clientId": "go-mqtt-client", "protocol": "mqtts"}, "subscribeTopics": {"TopicFirmwareReadDataRes": "readData/res", "TopicFirmwareWriteDataRes": "writeData/res", "TopicFirmwareReadDataPeriodicRes": "readData/periodic/res"}, "publishTopics": {"TopicFirmwareReadDataReq": "readData/req", "TopicFirmwareWriteDataReq": "writeData/req"}}