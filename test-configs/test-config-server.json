{"mqtt": {"broker": "rado-dev-broker.1iot.io", "port": 8885, "username": "platform_user", "password": "gayp3yw8wa4tm93dbtqo", "clientId": "go-mqtt-server", "protocol": "mqtts"}, "subscribeTopics": {"TopicArgusPullSensorData": "argus/bangalore/aatmunnOffice/pullSensorData", "TopicArgusReqControlData": "argus/bangalore/aatmunnOffice/reqControlData", "TopicArgusGetConfig": "argus/bangalore/aatmunnOffice/getConfig"}, "publishTopics": {"TopicArgusPeriodic": "argus/bangalore/aatmunnOffice/periodic", "TopicArgusResponseControlData": "argus/bangalore/aatmunnOffice/responseControlData", "TopicArgusConfig": "argus/bangalore/aatmunnOffice/config"}}