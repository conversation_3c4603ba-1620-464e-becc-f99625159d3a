package testutils

import (
	"program-manager/types"
)

// CreateDefaultClockConfig creates a default Clock configuration for testing
func CreateDefaultClockConfig() types.ClockConfig {
	return types.ClockConfig{
		ProgramName:    "Clock Program",
		Enabled:        true,
		Latitude:       17.407104033722273, // Hyderabad coordinates as default
		Longitude:      78.38716849147556,  // Hyderabad coordinates as default
		Timezone:       "Asia/Kolkata",
		NTPServer:      "pool.ntp.org",
		UpdateInterval: 10, // Update every 10 seconds for better visibility
		RedisTopics: types.ClockRedisTopics{
			CurrentTime: "hub:1:clock:currentTime",
			Date:        "hub:1:clock:date",
			DayOfWeek:   "hub:1:clock:dayOfWeek",
			Dawn:        "hub:1:clock:dawn",
			Dusk:        "hub:1:clock:dusk",
			IsDaytime:   "hub:1:clock:isDaytime",
		},
	}
}

// CreateEnhancedCEBConfig creates an enhanced CEB configuration for testing
func CreateEnhancedCEBConfig() types.CEBConfig {
	return types.CEBConfig{
		ProgramName: "climate energy balance",
		Enabled:     true,

		// Enhanced configuration structure with new Redis key format
		CEBInputRedisKeys: &types.CEBInputRedisKeys{
			ZoneTemperature:                  "hub:h1:io:sensorTemperature",
			BackupZoneTemperature:            "hub:h1:io:backupSensorTemperature",
			ZoneHumidity:                     "hub:h1:io:sensorHumidity",
			ShadePosition:                    "hub:h1:zone:z1:shade:shadePosition",
			CoolingTarget:                    "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget",
			HeatingTarget:                    "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget",
			DehumidifyVentilationTarget:      "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget",
			DehumidifyHeatingTarget:          "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget",
			MaxLimitForDehumidifyVentilation: "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent",
			MaxLimitForDehumidifyHeating:     "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat",
			OutdoorTemperature:               "hub:h1:instance:I1:weather:outdoorTemp",
			LightLevelNI:                     "hub:h1:instance:I1:weather:lightLevelNI",
			LightLevelI:                      "hub:h1:instance:I1:weather:lightLevelI",
		},

		CEBOutputRedisKeys: &types.CEBOutputRedisKeys{
			VentTempControl:          "hub:h1:zone:z1:instance:I1:ceb:ventTempControl",
			VentHumidityControl:      "hub:h1:zone:z1:instance:I1:ceb:ventHumidityControl",
			HighestVentRequest:       "hub:h1:zone:z1:instance:I1:ceb:highestVentRequest",
			SumVentRequests:          "hub:h1:zone:z1:instance:I1:ceb:sumVentRequests",
			HeatTempControl:          "hub:h1:zone:z1:instance:I1:ceb:heatTempControl",
			HeatHumidityControl:      "hub:h1:zone:z1:instance:I1:ceb:heatHumidityControl",
			HighestHeatRequest:       "hub:h1:zone:z1:instance:I1:ceb:highestHeatRequest",
			SumHeatRequests:          "hub:h1:zone:z1:instance:I1:ceb:sumHeatRequests",
			HeatingSystemTempRequest: "hub:h1:zone:z1:instance:I1:ceb:heatingSystemTempRequest",
		},

		HeatTuning: &types.HeatTuning{
			HeatReqForTemperatureControl: types.HeatReqForTemperatureControl{
				HeatingTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget",
					CrossModuleReqTime: 30,
				},
				ZoneTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:sensorTemperature",
					CrossModuleReqTime: 30,
				},
				BackupZoneTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:backupSensorTemperature",
					CrossModuleReqTime: 30,
				},
				HeatingProportionalSpanP: 4.0,
				HeatingIntegralTimeI:     50,
			},
			HeatReqForDehumidification: types.HeatReqForDehumidification{
				DehumidificationLimit: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat",
					CrossModuleReqTime: 30,
				},
				CurrentHumidity: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:sensorHumidity",
					CrossModuleReqTime: 30,
				},
				DehumidifyHeatTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget",
					CrossModuleReqTime: 30,
				},
				DehumidifyHeatOffset:            0.0,
				DehumidifyHeatProportionalSpanP: 33.3,
				DehumidifyHeatIntegralTimeI:     30,
			},
		},

		VentilationTuning: &types.VentilationTuning{
			VentReqForTemperatureControl: types.VentReqForTemperatureControl{
				CoolingTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget",
					CrossModuleReqTime: 30,
				},
				ZoneTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:sensorTemperature",
					CrossModuleReqTime: 30,
				},
				BackupZoneTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:backupSensorTemperature",
					CrossModuleReqTime: 30,
				},
				CoolingProportionalSpanP: 4.0,
				CoolingIntegralTimeI:     50,
			},
			VentReqForDehumidification: types.VentReqForDehumidification{
				DehumidifyVentTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget",
					CrossModuleReqTime: 30,
				},
				ZoneHumidity: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:sensorHumidity",
					CrossModuleReqTime: 30,
				},
				MaxLimitForDehumidifyVentilation: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent",
					CrossModuleReqTime: 30,
				},
				VentilationDehumidifyProportionalSpanP: 33.3,
				IntegralAccumulationTimeI:              30,
			},
		},
	}
}
