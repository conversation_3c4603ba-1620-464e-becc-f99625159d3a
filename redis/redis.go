package redis

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"

	"program-manager/types"

	"github.com/redis/go-redis/v9"
)

// Client holds the Redis client instance
var Client *redis.Client

// Initialize initializes the Redis client
func Initialize() {
	Client = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis server address
		Password: "",               // no password set
		DB:       0,                // use default DB
	})
}

// StoreSetpoints stores setpoints in Redis using CEB-compatible format
func StoreSetpoints(ctx context.Context, instance types.DiurnalInstance, period types.Period) error {
	// Store each setpoint as individual Redis key in CEB format
	// Format: hub:{hubId}:zone:{zoneId}:instance:{instanceId}:setpoint:{setpointName}
	for setpointName, value := range period.Setpoints {
		redisKey := fmt.Sprintf("hub:%s:zone:%s:instance:%s:setpoint:%s",
			instance.HubId, instance.ZoneId, instance.InstanceId, setpointName)

		// Store the setpoint value
		err := SetFloat(ctx, redisKey, value)
		if err != nil {
			return fmt.Errorf("failed to store setpoint %s in Redis: %v", setpointName, err)
		}

		// log.Printf("Diurnal: Stored setpoint %s = %.2f in Redis key: %s (TTL: 60 min)",
		// 	setpointName, value, redisKey)
	}

	return nil
}

// PrintAllSetpoints prints all setpoints stored in Redis using modern format
func PrintAllSetpoints(ctx context.Context) error {
	// Get all keys matching the modern CEB format
	keys, err := Client.Keys(ctx, "hub:*:zone:*:instance:*:setpoint:*").Result()
	if err != nil {
		return fmt.Errorf("failed to get keys from Redis: %v", err)
	}

	if len(keys) == 0 {
		log.Println("No setpoints found in Redis")
		return nil
	}

	log.Println("\nRetrieved setpoints from Redis:")
	for _, key := range keys {
		// Get the value for this key
		value, err := GetFloat(ctx, key)
		if err != nil {
			log.Printf("Warning: Failed to get value for key %s: %v", key, err)
			continue
		}

		log.Printf("  %s = %.2f", key, value)
	}
	return nil
}

// SetFloat stores a float64 value in Redis with 60-minute TTL
func SetFloat(ctx context.Context, key string, value float64) error {
	err := Client.Set(ctx, key, strconv.FormatFloat(value, 'f', -1, 64), 60*time.Minute).Err()
	if err != nil {
		return fmt.Errorf("failed to set float value in Redis: %v", err)
	}
	return nil
}

// GetFloat retrieves a float64 value from Redis
func GetFloat(ctx context.Context, key string) (float64, error) {
	valueStr, err := Client.Get(ctx, key).Result()
	if err != nil {
		return 0.0, fmt.Errorf("failed to get value from Redis: %v", err)
	}

	value, err := strconv.ParseFloat(valueStr, 64)
	if err != nil {
		return 0.0, fmt.Errorf("failed to parse float value: %v", err)
	}

	return value, nil
}

// SetString stores a string value in Redis with 60-minute TTL
func SetString(ctx context.Context, key string, value string) error {
	err := Client.Set(ctx, key, value, 60*time.Minute).Err()
	if err != nil {
		return fmt.Errorf("failed to set string value in Redis: %v", err)
	}
	return nil
}

// GetString retrieves a string value from Redis
func GetString(ctx context.Context, key string) (string, error) {
	value, err := Client.Get(ctx, key).Result()
	if err != nil {
		return "", fmt.Errorf("failed to get string value from Redis: %v", err)
	}
	return value, nil
}

// GetFloatWithDefault retrieves a float64 value from Redis, returning defaultValue if key doesn't exist or error occurs
func GetFloatWithDefault(ctx context.Context, key string, defaultValue float64) float64 {
	value, err := GetFloat(ctx, key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetStringWithDefault retrieves a string value from Redis, returning defaultValue if key doesn't exist or error occurs
func GetStringWithDefault(ctx context.Context, key string, defaultValue string) string {
	value, err := GetString(ctx, key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetFloatWithFallback retrieves a float64 value from Redis, trying backup key if primary fails, returning defaultValue if both fail
func GetFloatWithFallback(ctx context.Context, primaryKey, backupKey string, defaultValue float64) float64 {
	// Try primary key first
	if value, err := GetFloat(ctx, primaryKey); err == nil {
		return value
	}

	// Try backup key if provided
	if backupKey != "" {
		if value, err := GetFloat(ctx, backupKey); err == nil {
			return value
		}
	}

	// Return default if both fail
	return defaultValue
}

// SetFloatWithTTL stores a float64 value in Redis with custom TTL
func SetFloatWithTTL(ctx context.Context, key string, value float64, ttl time.Duration) error {
	err := Client.Set(ctx, key, strconv.FormatFloat(value, 'f', -1, 64), ttl).Err()
	if err != nil {
		return fmt.Errorf("failed to set float value in Redis: %v", err)
	}
	return nil
}

// SetStringWithTTL stores a string value in Redis with custom TTL
func SetStringWithTTL(ctx context.Context, key string, value string, ttl time.Duration) error {
	err := Client.Set(ctx, key, value, ttl).Err()
	if err != nil {
		return fmt.Errorf("failed to set string value in Redis: %v", err)
	}
	return nil
}

// BatchSetFloat stores multiple float values in Redis with 60-minute TTL
func BatchSetFloat(ctx context.Context, keyValues map[string]float64) error {
	for key, value := range keyValues {
		if err := SetFloat(ctx, key, value); err != nil {
			return fmt.Errorf("failed to set %s: %v", key, err)
		}
	}
	return nil
}

// BatchGetFloat retrieves multiple float values from Redis
func BatchGetFloat(ctx context.Context, keys []string) (map[string]float64, error) {
	result := make(map[string]float64)
	for _, key := range keys {
		if value, err := GetFloat(ctx, key); err == nil {
			result[key] = value
		}
	}
	return result, nil
}

// GetFloatWithLogging retrieves a float64 value with optional verbose logging
func GetFloatWithLogging(ctx context.Context, key string, defaultValue float64, valueName string, verbose bool) float64 {
	if value, err := GetFloat(ctx, key); err == nil {
		if verbose {
			log.Printf("Retrieved %s = %.1f from %s", valueName, value, key)
		}
		return value
	} else {
		if verbose {
			log.Printf("Using default %s = %.1f (Redis key %s not available: %v)", valueName, defaultValue, key, err)
		}
		return defaultValue
	}
}

// KeyExists checks if a key exists in Redis
func KeyExists(ctx context.Context, key string) bool {
	result := Client.Exists(ctx, key)
	return result.Val() > 0
}

// GetKeysPattern retrieves all keys matching a pattern
func GetKeysPattern(ctx context.Context, pattern string) ([]string, error) {
	keys, err := Client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get keys from Redis: %v", err)
	}
	return keys, nil
}
