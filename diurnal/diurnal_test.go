package diurnal

import (
	"testing"
	"time"

	"program-manager/types"
)

// TestCalculateRampRateWithRelativeTimes tests ramp rate calculation indirectly
func TestCalculateRampRateWithRelativeTimes(t *testing.T) {
	// Test that CalculateRampRate can handle relative times internally
	period1 := types.Period{
		PeriodId:  "1",
		EndTime:   "01:00 beforeDawn", // Relative time
		Setpoints: map[string]float64{"temperature": 20.0},
	}
	period2 := types.Period{
		PeriodId:  "2",
		StartTime: "02:00 afterDawn", // Relative time
		Setpoints: map[string]float64{"temperature": 24.0},
	}

	// This should work (relative times are resolved internally)
	_, err := CalculateRampRate(period1, period2, "temperature")
	if err != nil {
		t.Logf("Ramp rate calculation with relative times: %v (expected behavior)", err)
		// This is expected since relative time resolution is implemented
	}
}

// TestCalculateRampRate tests the ramp rate calculation with relative times
func TestCalculateRampRate(t *testing.T) {
	tests := []struct {
		name     string
		period1  types.Period
		period2  types.Period
		setpoint string
		expected float64
		hasError bool
	}{
		{
			name: "Basic ramp rate calculation",
			period1: types.Period{
				PeriodId:  "1",
				EndTime:   "08:00",
				Setpoints: map[string]float64{"temperature": 20.0},
			},
			period2: types.Period{
				PeriodId:  "2",
				StartTime: "10:00",
				Setpoints: map[string]float64{"temperature": 24.0},
			},
			setpoint: "temperature",
			expected: 0.0333, // (24-20)/(2*60) = 4/120 = 0.0333 per minute
			hasError: false,
		},
		{
			name: "Negative ramp rate",
			period1: types.Period{
				PeriodId:  "1",
				EndTime:   "18:00",
				Setpoints: map[string]float64{"temperature": 25.0},
			},
			period2: types.Period{
				PeriodId:  "2",
				StartTime: "21:00",
				Setpoints: map[string]float64{"temperature": 19.0},
			},
			setpoint: "temperature",
			expected: -0.0333, // (19-25)/(3*60) = -6/180 = -0.0333 per minute
			hasError: false,
		},
		{
			name: "Zero ramp rate - same setpoint",
			period1: types.Period{
				PeriodId:  "1",
				EndTime:   "12:00",
				Setpoints: map[string]float64{"temperature": 22.0},
			},
			period2: types.Period{
				PeriodId:  "2",
				StartTime: "14:00",
				Setpoints: map[string]float64{"temperature": 22.0},
			},
			setpoint: "temperature",
			expected: 0.0, // No change
			hasError: false,
		},
		{
			name: "Missing setpoint in period1",
			period1: types.Period{
				PeriodId:  "1",
				EndTime:   "12:00",
				Setpoints: map[string]float64{"humidity": 60.0},
			},
			period2: types.Period{
				PeriodId:  "2",
				StartTime: "14:00",
				Setpoints: map[string]float64{"temperature": 22.0},
			},
			setpoint: "temperature",
			expected: 0.0,
			hasError: true,
		},
		{
			name: "Missing setpoint in period2",
			period1: types.Period{
				PeriodId:  "1",
				EndTime:   "12:00",
				Setpoints: map[string]float64{"temperature": 20.0},
			},
			period2: types.Period{
				PeriodId:  "2",
				StartTime: "14:00",
				Setpoints: map[string]float64{"humidity": 65.0},
			},
			setpoint: "temperature",
			expected: 0.0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := CalculateRampRate(tt.period1, tt.period2, tt.setpoint)

			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Allow for small floating point differences
			if abs(result-tt.expected) > 0.001 {
				t.Errorf("Expected %.4f, got %.4f", tt.expected, result)
			}
		})
	}
}

// TestIsTimeInPeriod tests the time period checking function
func TestIsTimeInPeriod(t *testing.T) {
	// Create a test period for Monday 09:00-17:00
	period := types.Period{
		PeriodId:  "1",
		Name:      "Work Hours",
		Enabled:   true,
		StartTime: "09:00",
		EndTime:   "17:00",
		ActiveDays: map[string]bool{
			"monday":    true,
			"tuesday":   true,
			"wednesday": true,
			"thursday":  true,
			"friday":    true,
			"saturday":  false,
			"sunday":    false,
		},
	}

	tests := []struct {
		name     string
		time     time.Time
		expected bool
	}{
		{
			name:     "Monday 10:00 - should be active",
			time:     time.Date(2024, 6, 17, 10, 0, 0, 0, time.UTC), // Monday
			expected: true,
		},
		{
			name:     "Monday 08:00 - before start time",
			time:     time.Date(2024, 6, 17, 8, 0, 0, 0, time.UTC), // Monday
			expected: false,
		},
		{
			name:     "Monday 18:00 - after end time",
			time:     time.Date(2024, 6, 17, 18, 0, 0, 0, time.UTC), // Monday
			expected: false,
		},
		{
			name:     "Saturday 10:00 - inactive day",
			time:     time.Date(2024, 6, 22, 10, 0, 0, 0, time.UTC), // Saturday
			expected: false,
		},
		{
			name:     "Sunday 10:00 - inactive day",
			time:     time.Date(2024, 6, 23, 10, 0, 0, 0, time.UTC), // Sunday
			expected: false,
		},
		{
			name:     "Friday 16:59 - just before end",
			time:     time.Date(2024, 6, 21, 16, 59, 0, 0, time.UTC), // Friday
			expected: true,
		},
		{
			name:     "Tuesday 09:01 - just after start",
			time:     time.Date(2024, 6, 18, 9, 1, 0, 0, time.UTC), // Tuesday
			expected: true,
		},
		{
			name:     "Wednesday 17:00 - exactly at end",
			time:     time.Date(2024, 6, 19, 17, 0, 0, 0, time.UTC), // Wednesday
			expected: false,                                         // End time is exclusive
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isTimeInPeriod(tt.time, period)
			if result != tt.expected {
				t.Errorf("Expected %v, got %v for %s", tt.expected, result, tt.time.Format("Monday 15:04"))
			}
		})
	}
}

// TestGetCurrentState tests the diurnal state determination
func TestGetCurrentState(t *testing.T) {
	// Create a test instance with periods
	instance := types.DiurnalInstance{
		InstanceId:  "1",
		ProgramName: "Test Diurnal",
		Enabled:     true,
		HubId:       "1",
		ZoneId:      "1",
		Periods: []types.Period{
			{
				PeriodId:  "1",
				Name:      "Morning",
				Enabled:   true,
				StartTime: "08:00",
				EndTime:   "12:00",
				ActiveDays: map[string]bool{
					"monday": true, "tuesday": true, "wednesday": true,
					"thursday": true, "friday": true, "saturday": false, "sunday": false,
				},
				Setpoints: map[string]float64{"temperature": 22.0},
			},
			{
				PeriodId:  "2",
				Name:      "Afternoon",
				Enabled:   true,
				StartTime: "13:00",
				EndTime:   "17:00",
				ActiveDays: map[string]bool{
					"monday": true, "tuesday": true, "wednesday": true,
					"thursday": true, "friday": true, "saturday": false, "sunday": false,
				},
				Setpoints: map[string]float64{"temperature": 24.0},
			},
		},
	}

	tests := []struct {
		name          string
		time          time.Time
		expectedState types.PeriodState
		expectPeriod  bool
	}{
		{
			name:          "Monday 10:00 - in morning period",
			time:          time.Date(2024, 6, 17, 10, 0, 0, 0, time.UTC), // Monday
			expectedState: types.StateInPeriod,
			expectPeriod:  true,
		},
		{
			name:          "Monday 12:30 - between periods (ramping)",
			time:          time.Date(2024, 6, 17, 12, 30, 0, 0, time.UTC), // Monday
			expectedState: types.StateRamping,
			expectPeriod:  true,
		},
		{
			name:          "Monday 15:00 - in afternoon period",
			time:          time.Date(2024, 6, 17, 15, 0, 0, 0, time.UTC), // Monday
			expectedState: types.StateInPeriod,
			expectPeriod:  true,
		},
		{
			name:          "Monday 20:00 - idle time",
			time:          time.Date(2024, 6, 17, 20, 0, 0, 0, time.UTC), // Monday
			expectedState: types.StateIdle,
			expectPeriod:  false,
		},
		{
			name:          "Saturday 10:00 - inactive day",
			time:          time.Date(2024, 6, 22, 10, 0, 0, 0, time.UTC), // Saturday
			expectedState: types.StateIdle,
			expectPeriod:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			state, currentPeriod, nextPeriod := GetCurrentState(tt.time, instance)

			if state != tt.expectedState {
				t.Errorf("Expected state %v, got %v", tt.expectedState, state)
			}

			if tt.expectPeriod && currentPeriod == nil && nextPeriod == nil {
				t.Errorf("Expected period but got nil")
			} else if !tt.expectPeriod && (currentPeriod != nil || nextPeriod != nil) {
				if currentPeriod != nil {
					t.Errorf("Expected no period but got current period %s", currentPeriod.PeriodId)
				}
				if nextPeriod != nil {
					t.Errorf("Expected no period but got next period %s", nextPeriod.PeriodId)
				}
			}
		})
	}
}

// TestDisabledInstance tests behavior with disabled instance
func TestDisabledInstance(t *testing.T) {
	instance := types.DiurnalInstance{
		InstanceId:  "1",
		ProgramName: "Disabled Test",
		Enabled:     false, // Disabled
		HubId:       "1",
		ZoneId:      "1",
		Periods: []types.Period{
			{
				PeriodId:   "1",
				Name:       "Test Period",
				Enabled:    true,
				StartTime:  "10:00",
				EndTime:    "14:00",
				ActiveDays: map[string]bool{"monday": true},
				Setpoints:  map[string]float64{"temperature": 22.0},
			},
		},
	}

	// Monday 12:00 - would normally be active
	testTime := time.Date(2024, 6, 17, 12, 0, 0, 0, time.UTC)

	state, currentPeriod, nextPeriod := GetCurrentState(testTime, instance)

	if state != types.StateIdle {
		t.Errorf("Expected StateIdle for disabled instance, got %v", state)
	}

	if currentPeriod != nil {
		t.Errorf("Expected no current period for disabled instance, got period %s", currentPeriod.PeriodId)
	}

	if nextPeriod != nil {
		t.Errorf("Expected no next period for disabled instance, got period %s", nextPeriod.PeriodId)
	}
}

// Helper function for absolute value
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

func TestDetectAndHandleOverlaps(t *testing.T) {
	// Create a test configuration with overlapping periods
	config := types.DiurnalSetpointConfig{
		Instances: []types.DiurnalInstance{
			{
				InstanceId:  "test1",
				ProgramName: "Overlap Test",
				Enabled:     true,
				HubId:       "1",
				ZoneId:      "1",
				Periods: []types.Period{
					{
						PeriodId:     "period1",
						Name:         "Morning Period",
						Enabled:      true,
						PeriodStatus: "active",
						StartTime:    "08:00",
						EndTime:      "12:00",
						ActiveDays: map[string]bool{
							"monday": true,
						},
						Setpoints: map[string]float64{
							"temperature": 22.0,
						},
					},
					{
						PeriodId:     "period2",
						Name:         "Afternoon Period",
						Enabled:      true,
						PeriodStatus: "active",
						StartTime:    "12:15", // Only 15 minutes gap - should be marked as overlap
						EndTime:      "16:00",
						ActiveDays: map[string]bool{
							"monday": true,
						},
						Setpoints: map[string]float64{
							"temperature": 24.0,
						},
					},
					{
						PeriodId:     "period3",
						Name:         "Evening Period",
						Enabled:      true,
						PeriodStatus: "active",
						StartTime:    "17:00", // 60 minutes gap - should be fine
						EndTime:      "20:00",
						ActiveDays: map[string]bool{
							"monday": true,
						},
						Setpoints: map[string]float64{
							"temperature": 20.0,
						},
					},
				},
			},
		},
	}

	// Convert to static times first
	staticConfig, err := ConvertRelativeTimesToStatic(&config, nil)
	if err != nil {
		t.Fatalf("Failed to convert to static times: %v", err)
	}

	// Test overlap detection
	DetectAndHandleOverlaps(staticConfig)

	// Check that period2 is marked as overlapped
	if staticConfig.Instances[0].Periods[1].PeriodStatus != "notUsedOverlap" {
		t.Errorf("Expected period2 to be marked as 'notUsedOverlap', got '%s'",
			staticConfig.Instances[0].Periods[1].PeriodStatus)
	}

	// Check that period1 and period3 are not marked as overlapped
	if staticConfig.Instances[0].Periods[0].PeriodStatus == "notUsedOverlap" {
		t.Errorf("Expected period1 to NOT be marked as overlapped")
	}
	if staticConfig.Instances[0].Periods[2].PeriodStatus == "notUsedOverlap" {
		t.Errorf("Expected period3 to NOT be marked as overlapped")
	}
}

func TestOverlappedPeriodSkipping(t *testing.T) {
	// Create a period marked as overlapped
	period := types.Period{
		PeriodId:     "overlapped",
		Name:         "Overlapped Period",
		Enabled:      true,
		PeriodStatus: "notUsedOverlap",
		StartTime:    "10:00",
		EndTime:      "12:00",
		ActiveDays: map[string]bool{
			"monday": true,
		},
		Setpoints: map[string]float64{
			"temperature": 22.0,
		},
	}

	// Test that overlapped period is not considered active
	testTime := time.Date(2023, 1, 2, 10, 30, 0, 0, time.UTC) // Monday 10:30
	isActive := isTimeInPeriod(testTime, period)

	if isActive {
		t.Errorf("Expected overlapped period to be inactive, but it was considered active")
	}
}
