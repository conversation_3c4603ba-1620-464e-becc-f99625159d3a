package diurnal

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"program-manager/redis"
	"program-manager/types"
)

// TimeResolver handles all time resolution logic including dawn/dusk calculations
type TimeResolver struct {
	clockState *types.ClockState
	mutex      sync.RWMutex
}

// NewTimeResolver creates a new TimeResolver instance
func NewTimeResolver(clockState *types.ClockState) *TimeResolver {
	return &TimeResolver{
		clockState: clockState,
	}
}

// SetClockState updates the clock state for time calculations
func (tr *TimeResolver) SetClockState(clockState *types.ClockState) {
	tr.mutex.Lock()
	defer tr.mutex.Unlock()
	tr.clockState = clockState
}

// GetClockState returns the current clock state
func (tr *TimeResolver) GetClockState() *types.ClockState {
	tr.mutex.RLock()
	defer tr.mutex.RUnlock()
	return tr.clockState
}

// ResolveDawnDusk returns dawn and dusk times, either from clock state or defaults
func (tr *TimeResolver) ResolveDawnDusk(referenceTime time.Time) (dawn, dusk time.Time) {
	tr.mutex.RLock()
	defer tr.mutex.RUnlock()

	if tr.clockState != nil {
		// Parse dawn and dusk times from clock state (try both HH:MM:SS and HH:MM formats)
		if d, err := time.Parse("15:04:05", tr.clockState.Dawn); err == nil {
			dawn = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				d.Hour(), d.Minute(), 0, 0, referenceTime.Location())
		} else if d, err := time.Parse("15:04", tr.clockState.Dawn); err == nil {
			dawn = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				d.Hour(), d.Minute(), 0, 0, referenceTime.Location())
		} else {
			log.Printf("Warning: Failed to parse dawn time '%s', using default: %v", tr.clockState.Dawn, err)
			dawn = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				6, 30, 0, 0, referenceTime.Location())
		}

		if d, err := time.Parse("15:04:05", tr.clockState.Dusk); err == nil {
			dusk = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				d.Hour(), d.Minute(), 0, 0, referenceTime.Location())
		} else if d, err := time.Parse("15:04", tr.clockState.Dusk); err == nil {
			dusk = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				d.Hour(), d.Minute(), 0, 0, referenceTime.Location())
		} else {
			log.Printf("Warning: Failed to parse dusk time '%s', using default: %v", tr.clockState.Dusk, err)
			dusk = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				18, 45, 0, 0, referenceTime.Location())
		}
	} else {
		// Use default dawn/dusk times if no clock state available
		dawn = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
			6, 30, 0, 0, referenceTime.Location())
		dusk = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
			18, 45, 0, 0, referenceTime.Location())
		log.Printf("Warning: Using default dawn/dusk times (06:30/18:45) - clock state not available")
	}

	return dawn, dusk
}

// Global time resolver instance for backward compatibility
var globalTimeResolver *TimeResolver

// setGlobalClockState sets the global clock state for relative time calculations (backward compatibility)
func setGlobalClockState(clockState *types.ClockState) {
	if globalTimeResolver == nil {
		globalTimeResolver = NewTimeResolver(clockState)
	} else {
		globalTimeResolver.SetClockState(clockState)
	}
}

// getGlobalClockState gets the global clock state for relative time calculations (internal)
func getGlobalClockState() *types.ClockState {
	if globalTimeResolver == nil {
		return nil
	}
	return globalTimeResolver.GetClockState()
}

// GetGlobalClockState gets the global clock state for relative time calculations (exported)
func GetGlobalClockState() *types.ClockState {
	return getGlobalClockState()
}

// ResolveRelativeTime converts relative time to absolute time using dawn/dusk from TimeResolver
func (tr *TimeResolver) ResolveRelativeTime(timeStr string, referenceTime time.Time) (string, error) {
	// Check if it's a relative time
	if !strings.Contains(timeStr, "beforeDawn") && !strings.Contains(timeStr, "afterDawn") &&
		!strings.Contains(timeStr, "beforeDusk") && !strings.Contains(timeStr, "afterDusk") {
		return timeStr, nil // Return as-is if not relative
	}

	// Get dawn and dusk times
	dawnTime, duskTime := tr.ResolveDawnDusk(referenceTime)

	// Parse the time and reference
	parts := strings.Fields(timeStr)
	if len(parts) != 2 {
		return "", fmt.Errorf("invalid relative time format: %s", timeStr)
	}

	timePart := parts[0]
	reference := parts[1]

	// Parse the time offset
	offsetTime, err := time.Parse("15:04", timePart)
	if err != nil {
		return "", fmt.Errorf("invalid time in relative format: %v", err)
	}

	offset := time.Duration(offsetTime.Hour())*time.Hour + time.Duration(offsetTime.Minute())*time.Minute

	var baseTime time.Time
	switch reference {
	case "beforeDawn":
		baseTime = dawnTime.Add(-offset)
	case "afterDawn":
		baseTime = dawnTime.Add(offset)
	case "beforeDusk":
		baseTime = duskTime.Add(-offset)
	case "afterDusk":
		baseTime = duskTime.Add(offset)
	default:
		return "", fmt.Errorf("invalid time reference: %s", reference)
	}

	return baseTime.Format("15:04"), nil
}

// CalculateRampRate calculates the ramp rate between two periods for a given setpoint
// Note: This function handles both static and relative times by converting relative times first
func CalculateRampRate(period1, period2 types.Period, setpointName string) (float64, error) {
	// Convert relative times to static times if needed
	var resolvedEndTime, resolvedStartTime string
	var err error

	if globalTimeResolver != nil {
		// Use global time resolver to convert relative times
		resolvedEndTime, err = globalTimeResolver.ResolveRelativeTime(period1.EndTime, time.Now())
		if err != nil {
			return 0, fmt.Errorf("failed to resolve end time '%s' for period %s: %v", period1.EndTime, period1.PeriodId, err)
		}
		resolvedStartTime, err = globalTimeResolver.ResolveRelativeTime(period2.StartTime, time.Now())
		if err != nil {
			return 0, fmt.Errorf("failed to resolve start time '%s' for period %s: %v", period2.StartTime, period2.PeriodId, err)
		}
	} else {
		// Fallback: assume times are already static
		resolvedEndTime = period1.EndTime
		resolvedStartTime = period2.StartTime
	}

	// Parse resolved times
	start1, err := time.Parse("15:04", resolvedEndTime)
	if err != nil {
		return 0, fmt.Errorf("invalid end time format: %v", err)
	}

	start2, err := time.Parse("15:04", resolvedStartTime)
	if err != nil {
		return 0, fmt.Errorf("invalid start time format: %v", err)
	}

	// Calculate time difference in minutes
	timeDiff := start2.Sub(start1).Minutes()
	if timeDiff <= 0 {
		return 0, fmt.Errorf("period 2 must start after period 1")
	}

	// Get setpoint values
	sp1, exists1 := period1.Setpoints[setpointName]
	sp2, exists2 := period2.Setpoints[setpointName]
	if !exists1 || !exists2 {
		return 0, fmt.Errorf("setpoint %s not found in one or both periods", setpointName)
	}

	// Calculate setpoint difference
	spDiff := sp2 - sp1

	// Calculate ramp rate (change per minute)
	rampRate := spDiff / timeDiff

	return rampRate, nil
}

// ConvertRelativeTimesToStatic converts all relative times in the configuration to static times
func ConvertRelativeTimesToStatic(config *types.DiurnalSetpointConfig, clockState *types.ClockState) (*types.DiurnalSetpointConfig, error) {
	// Create a deep copy of the configuration
	staticConfig := &types.DiurnalSetpointConfig{
		Instances: make([]types.DiurnalInstance, len(config.Instances)),
	}

	// Set up time resolver for relative time calculations
	timeResolver := NewTimeResolver(clockState)
	now := time.Now()

	log.Println("Diurnal: Converting relative times to static times...")

	for i, instance := range config.Instances {
		staticConfig.Instances[i] = types.DiurnalInstance{
			InstanceId:  instance.InstanceId,
			ProgramName: instance.ProgramName,
			Enabled:     instance.Enabled,
			HubId:       instance.HubId,
			ZoneId:      instance.ZoneId,
			Periods:     make([]types.Period, len(instance.Periods)),
		}

		for j, period := range instance.Periods {
			// Copy the period
			staticPeriod := types.Period{
				PeriodId:     period.PeriodId,
				Name:         period.Name,
				Enabled:      period.Enabled,
				PeriodStatus: period.PeriodStatus,
				ActiveDays:   make(map[string]bool),
				Setpoints:    make(map[string]float64),
			}

			// Copy active days
			for day, active := range period.ActiveDays {
				staticPeriod.ActiveDays[day] = active
			}

			// Copy setpoints
			for name, value := range period.Setpoints {
				staticPeriod.Setpoints[name] = value
			}

			// Convert start time
			resolvedStartTime, err := timeResolver.ResolveRelativeTime(period.StartTime, now)
			if err != nil {
				return nil, fmt.Errorf("failed to resolve start time for period %s in instance %s: %v",
					period.PeriodId, instance.InstanceId, err)
			}
			staticPeriod.StartTime = resolvedStartTime

			// Convert end time
			resolvedEndTime, err := timeResolver.ResolveRelativeTime(period.EndTime, now)
			if err != nil {
				return nil, fmt.Errorf("failed to resolve end time for period %s in instance %s: %v",
					period.PeriodId, instance.InstanceId, err)
			}
			staticPeriod.EndTime = resolvedEndTime

			// Log conversion if it was a relative time
			if period.StartTime != resolvedStartTime || period.EndTime != resolvedEndTime {
				// log.Printf("Diurnal: Converted period %s in instance %s: %s-%s → %s-%s",
				// period.PeriodId, instance.InstanceId,
				// period.StartTime, period.EndTime,
				// resolvedStartTime, resolvedEndTime)
			}

			staticConfig.Instances[i].Periods[j] = staticPeriod
		}
	}

	log.Println("Diurnal: Relative time conversion completed")
	return staticConfig, nil
}

// DetectAndHandleOverlaps detects periods with less than 30 minutes gap and marks overlapping periods
// Note: This function now expects static times (no relative time resolution needed)
func DetectAndHandleOverlaps(config *types.DiurnalSetpointConfig) {
	for instanceIdx := range config.Instances {
		instance := &config.Instances[instanceIdx]
		if !instance.Enabled || len(instance.Periods) < 2 {
			continue
		}

		// Sort periods by start time for each day
		for dayName := range map[string]bool{
			"monday": true, "tuesday": true, "wednesday": true, "thursday": true,
			"friday": true, "saturday": true, "sunday": true,
		} {
			var dayPeriods []*types.Period
			var dayIndices []int

			// Collect periods active on this day
			for i := range instance.Periods {
				period := &instance.Periods[i]
				if period.Enabled && period.ActiveDays[dayName] {
					dayPeriods = append(dayPeriods, period)
					dayIndices = append(dayIndices, i)
				}
			}

			if len(dayPeriods) < 2 {
				continue
			}

			// Sort periods by start time (now static times)
			for i := 0; i < len(dayPeriods)-1; i++ {
				for j := i + 1; j < len(dayPeriods); j++ {
					timeI, err1 := time.Parse("15:04", dayPeriods[i].StartTime)
					timeJ, err2 := time.Parse("15:04", dayPeriods[j].StartTime)
					if err1 == nil && err2 == nil && timeI.After(timeJ) {
						dayPeriods[i], dayPeriods[j] = dayPeriods[j], dayPeriods[i]
						dayIndices[i], dayIndices[j] = dayIndices[j], dayIndices[i]
					}
				}
			}

			// Check for overlaps between consecutive periods
			for i := 0; i < len(dayPeriods)-1; i++ {
				currentPeriod := dayPeriods[i]
				nextPeriod := dayPeriods[i+1]
				nextIndex := dayIndices[i+1]

				// Parse static times directly
				endTimeParsed, err1 := time.Parse("15:04", currentPeriod.EndTime)
				startTimeParsed, err2 := time.Parse("15:04", nextPeriod.StartTime)

				if err1 != nil || err2 != nil {
					continue
				}

				// Calculate time difference in minutes
				timeDiff := startTimeParsed.Sub(endTimeParsed).Minutes()

				// If gap is less than 30 minutes, mark next period as overlapped
				if timeDiff < 30 && timeDiff >= 0 {
					config.Instances[instanceIdx].Periods[nextIndex].PeriodStatus = "notUsedOverlap"
					log.Printf("Diurnal: Period %s in instance %s marked as 'notUsedOverlap' - gap of %.1f minutes with previous period on %s",
						nextPeriod.PeriodId, instance.InstanceId, timeDiff, dayName)
				}
			}
		}
	}
}

// ProcessInstances processes diurnal instances in parallel with optional clock state for relative times
func ProcessInstances(ctx context.Context, config types.DiurnalSetpointConfig, clockState *types.ClockState) {
	// Set global clock state for relative time calculations (for backward compatibility)
	if clockState != nil {
		log.Printf("Diurnal: Using clock state for relative time calculations: Dawn=%s, Dusk=%s",
			clockState.Dawn, clockState.Dusk)
		setGlobalClockState(clockState)
	} else {
		log.Println("Diurnal: Warning - No clock state provided, using default dawn/dusk times")
	}

	// Convert relative times to static times once
	staticConfig, err := ConvertRelativeTimesToStatic(&config, clockState)
	if err != nil {
		log.Fatalf("Failed to convert relative times to static times: %v", err)
		return
	}

	// Detect and handle overlaps using static times
	DetectAndHandleOverlaps(staticConfig)
	// Create a channel to receive results
	results := make(chan struct {
		instanceID string
		period     types.Period
		err        error
	})

	// Process each enabled instance in parallel
	for _, instance := range staticConfig.Instances {
		if instance.Enabled {
			go func(inst types.DiurnalInstance) {
				for i, period := range inst.Periods {
					// Skip periods marked as overlapped
					if period.PeriodStatus == "notUsedOverlap" {
						log.Printf("Diurnal: Skipping period %s in instance %s - marked as overlapped",
							period.PeriodId, inst.InstanceId)
						results <- struct {
							instanceID string
							period     types.Period
							err        error
						}{inst.InstanceId, period, nil}
						continue
					}

					err := redis.StoreSetpoints(ctx, inst, period)
					if err != nil {
						results <- struct {
							instanceID string
							period     types.Period
							err        error
						}{inst.InstanceId, period, err}
						continue
					}

					// Calculate and store ramp rate if this is not the last period
					if i < len(inst.Periods)-1 {
						nextPeriod := inst.Periods[i+1]
						// Calculate ramp rate for each setpoint
						for setpointName := range period.Setpoints {
							rampRate, err := CalculateRampRate(period, nextPeriod, setpointName)
							if err != nil {
								log.Printf("Warning: Failed to calculate ramp rate for instance %s period %s setpoint %s: %v",
									inst.InstanceId, period.PeriodId, setpointName, err)
								continue
							}

							// Ramp rate calculated and logged (no legacy storage needed)
							log.Printf("Ramp rate for instance %s period %s setpoint %s: %.2f per minute",
								inst.InstanceId, period.PeriodId, setpointName, rampRate)
						}
					}

					results <- struct {
						instanceID string
						period     types.Period
						err        error
					}{inst.InstanceId, period, nil}
				}
			}(instance)
		}
	}

	// Collect results
	processedCount := 0
	totalPeriods := 0
	for _, instance := range staticConfig.Instances {
		if instance.Enabled {
			totalPeriods += len(instance.Periods)
		}
	}

	// Track if we've printed the first period's end time
	firstPeriodPrinted := false

	for processedCount < totalPeriods {
		result := <-results
		processedCount++

		if result.err != nil {
			log.Printf("Warning: Failed to store setpoints for instance %s period %s in Redis: %v",
				result.instanceID, result.period.PeriodId, result.err)
		}

		// Print end time of first period
		if !firstPeriodPrinted {
			log.Printf("\nEnd time of first period: %s", result.period.EndTime)
			firstPeriodPrinted = true
		}
	}
}

// GetCurrentState determines the current state of a diurnal instance
func GetCurrentState(currentTime time.Time, instance types.DiurnalInstance) (types.PeriodState, *types.Period, *types.Period) {
	if !instance.Enabled {
		return types.StateIdle, nil, nil
	}

	// Check if currently in an active period
	for i := range instance.Periods {
		period := &instance.Periods[i]
		if period.Enabled && isTimeInPeriod(currentTime, *period) {
			return types.StateInPeriod, period, nil
		}
	}

	// Check if currently in a ramp between periods
	currentPeriod, nextPeriod := findRampPeriods(currentTime, instance)
	if currentPeriod != nil && nextPeriod != nil {
		return types.StateRamping, currentPeriod, nextPeriod
	}

	return types.StateIdle, nil, nil
}

// isTimeInPeriod checks if the current time falls within a period
// Note: This function handles both static and relative times by converting relative times first
func isTimeInPeriod(currentTime time.Time, period types.Period) bool {
	// Skip periods marked as overlapped
	if period.PeriodStatus == "notUsedOverlap" {
		return false
	}

	// Check day of week
	dayName := strings.ToLower(currentTime.Weekday().String())
	if !period.ActiveDays[dayName] {
		return false
	}

	// Convert relative times to static times if needed
	var resolvedStartTime, resolvedEndTime string
	var err error

	if globalTimeResolver != nil {
		// Use global time resolver to convert relative times
		resolvedStartTime, err = globalTimeResolver.ResolveRelativeTime(period.StartTime, currentTime)
		if err != nil {
			log.Printf("Warning: Failed to resolve start time '%s': %v", period.StartTime, err)
			return false
		}
		resolvedEndTime, err = globalTimeResolver.ResolveRelativeTime(period.EndTime, currentTime)
		if err != nil {
			log.Printf("Warning: Failed to resolve end time '%s': %v", period.EndTime, err)
			return false
		}
	} else {
		// Fallback: assume times are already static
		resolvedStartTime = period.StartTime
		resolvedEndTime = period.EndTime
	}

	// Parse resolved period times
	startTime, err := time.Parse("15:04", resolvedStartTime)
	if err != nil {
		log.Printf("Warning: Failed to parse resolved start time '%s': %v", resolvedStartTime, err)
		return false
	}
	endTime, err := time.Parse("15:04", resolvedEndTime)
	if err != nil {
		log.Printf("Warning: Failed to parse resolved end time '%s': %v", resolvedEndTime, err)
		return false
	}

	// Create time objects for comparison (same date, different times)
	currentTimeOfDay := time.Date(0, 1, 1, currentTime.Hour(), currentTime.Minute(), currentTime.Second(), 0, time.UTC)
	startTimeOfDay := time.Date(0, 1, 1, startTime.Hour(), startTime.Minute(), 0, 0, time.UTC)
	endTimeOfDay := time.Date(0, 1, 1, endTime.Hour(), endTime.Minute(), 0, 0, time.UTC)

	// Handle periods that cross midnight
	if endTimeOfDay.Before(startTimeOfDay) {
		// Period crosses midnight (e.g., 22:00 to 06:00)
		return currentTimeOfDay.After(startTimeOfDay) || currentTimeOfDay.Before(endTimeOfDay)
	}

	// Normal period within same day
	return currentTimeOfDay.After(startTimeOfDay) && currentTimeOfDay.Before(endTimeOfDay)
}

// findRampPeriods finds the current and next periods for ramping
func findRampPeriods(currentTime time.Time, instance types.DiurnalInstance) (*types.Period, *types.Period) {
	if !instance.Enabled || len(instance.Periods) < 2 {
		return nil, nil
	}

	// Get current day of week
	dayName := strings.ToLower(currentTime.Weekday().String())
	currentTimeOfDay := time.Date(0, 1, 1, currentTime.Hour(), currentTime.Minute(), currentTime.Second(), 0, time.UTC)

	// Find periods that are active today and sort them by start time
	var todaysPeriods []types.Period
	for _, period := range instance.Periods {
		if period.Enabled && period.ActiveDays[dayName] && period.PeriodStatus != "notUsedOverlap" {
			todaysPeriods = append(todaysPeriods, period)
		}
	}

	if len(todaysPeriods) < 2 {
		return nil, nil
	}

	// Sort periods by start time (resolve relative times first)
	for i := 0; i < len(todaysPeriods)-1; i++ {
		for j := i + 1; j < len(todaysPeriods); j++ {
			// Resolve relative times for comparison
			var startTimeI, startTimeJ string
			if globalTimeResolver != nil {
				startTimeI, _ = globalTimeResolver.ResolveRelativeTime(todaysPeriods[i].StartTime, currentTime)
				startTimeJ, _ = globalTimeResolver.ResolveRelativeTime(todaysPeriods[j].StartTime, currentTime)
			} else {
				startTimeI = todaysPeriods[i].StartTime
				startTimeJ = todaysPeriods[j].StartTime
			}

			startI, errI := time.Parse("15:04", startTimeI)
			startJ, errJ := time.Parse("15:04", startTimeJ)
			if errI == nil && errJ == nil && startI.After(startJ) {
				todaysPeriods[i], todaysPeriods[j] = todaysPeriods[j], todaysPeriods[i]
			}
		}
	}

	// Check if we're in a ramp between consecutive periods
	for i := 0; i < len(todaysPeriods)-1; i++ {
		currentPeriod := todaysPeriods[i]
		nextPeriod := todaysPeriods[i+1]

		// Resolve relative times to static times
		var resolvedEndTime, resolvedStartTime string
		if globalTimeResolver != nil {
			resolvedEndTime, _ = globalTimeResolver.ResolveRelativeTime(currentPeriod.EndTime, currentTime)
			resolvedStartTime, _ = globalTimeResolver.ResolveRelativeTime(nextPeriod.StartTime, currentTime)
		} else {
			resolvedEndTime = currentPeriod.EndTime
			resolvedStartTime = nextPeriod.StartTime
		}

		// Parse resolved times
		endTime, err := time.Parse("15:04", resolvedEndTime)
		if err != nil {
			continue
		}
		startTime, err := time.Parse("15:04", resolvedStartTime)
		if err != nil {
			continue
		}

		endTimeOfDay := time.Date(0, 1, 1, endTime.Hour(), endTime.Minute(), 0, 0, time.UTC)
		startTimeOfDay := time.Date(0, 1, 1, startTime.Hour(), startTime.Minute(), 0, 0, time.UTC)

		// Check if current time is between end of current period and start of next period
		if currentTimeOfDay.After(endTimeOfDay) && currentTimeOfDay.Before(startTimeOfDay) {
			return &currentPeriod, &nextPeriod
		}
	}

	return nil, nil
}

// CalculateCurrentSetpoints calculates the current setpoint values for an instance
func CalculateCurrentSetpoints(currentTime time.Time, instance types.DiurnalInstance) (map[string]float64, error) {
	state, currentPeriod, nextPeriod := GetCurrentState(currentTime, instance)

	switch state {
	case types.StateInPeriod:
		// Return period setpoints directly
		return currentPeriod.Setpoints, nil

	case types.StateRamping:
		// Calculate ramped values
		result := make(map[string]float64)

		// Convert relative end time to static time if needed
		var resolvedEndTime string
		var err error
		if globalTimeResolver != nil {
			resolvedEndTime, err = globalTimeResolver.ResolveRelativeTime(currentPeriod.EndTime, currentTime)
			if err != nil {
				return nil, fmt.Errorf("failed to resolve end time '%s': %v", currentPeriod.EndTime, err)
			}
		} else {
			resolvedEndTime = currentPeriod.EndTime
		}

		// Parse resolved end time
		endTime, err := time.Parse("15:04", resolvedEndTime)
		if err != nil {
			return nil, fmt.Errorf("invalid end time format: %v", err)
		}

		endDateTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(),
			endTime.Hour(), endTime.Minute(), 0, 0, currentTime.Location())
		minutesElapsed := currentTime.Sub(endDateTime).Minutes()

		// Calculate ramped setpoints
		for setpointName, startValue := range currentPeriod.Setpoints {
			rampRate, err := CalculateRampRate(*currentPeriod, *nextPeriod, setpointName)
			if err != nil {
				return nil, fmt.Errorf("failed to calculate ramp rate for %s: %v", setpointName, err)
			}

			currentValue := startValue + (rampRate * minutesElapsed)
			result[setpointName] = currentValue
		}

		return result, nil

	case types.StateIdle:
		// Return empty map or default values
		return make(map[string]float64), nil

	default:
		return nil, fmt.Errorf("unknown state: %v", state)
	}
}

// GetDiurnalState returns the complete state information for an instance
func GetDiurnalState(currentTime time.Time, instance types.DiurnalInstance) types.DiurnalState {
	state, currentPeriod, nextPeriod := GetCurrentState(currentTime, instance)

	diurnalState := types.DiurnalState{
		InstanceID:    instance.InstanceId,
		State:         state,
		CurrentPeriod: currentPeriod,
		NextPeriod:    nextPeriod,
		LastUpdated:   currentTime.Format("15:04:05"),
	}

	// Calculate current setpoint values
	currentValues, err := CalculateCurrentSetpoints(currentTime, instance)
	if err != nil {
		log.Printf("Warning: Failed to calculate current setpoints for instance %s: %v", instance.InstanceId, err)
		currentValues = make(map[string]float64)
	}
	diurnalState.CurrentValues = currentValues

	// Calculate minutes elapsed if ramping
	if state == types.StateRamping && currentPeriod != nil {
		// Convert relative end time to static time if needed
		var resolvedEndTime string
		if globalTimeResolver != nil {
			resolvedEndTime, err = globalTimeResolver.ResolveRelativeTime(currentPeriod.EndTime, currentTime)
			if err != nil {
				log.Printf("Warning: Failed to resolve end time '%s' for minutes elapsed calculation: %v", currentPeriod.EndTime, err)
				resolvedEndTime = currentPeriod.EndTime // fallback
			}
		} else {
			resolvedEndTime = currentPeriod.EndTime
		}

		// Parse resolved end time
		endTime, err := time.Parse("15:04", resolvedEndTime)
		if err == nil {
			endDateTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(),
				endTime.Hour(), endTime.Minute(), 0, 0, currentTime.Location())
			diurnalState.MinutesElapsed = currentTime.Sub(endDateTime).Minutes()
		}
	}

	return diurnalState
}

// PrintPeriodOverlapStatus prints information about periods marked as overlapped
func PrintPeriodOverlapStatus(config types.DiurnalSetpointConfig) {
	log.Println("\n=== Period Overlap Status ===")

	hasOverlaps := false
	for _, instance := range config.Instances {
		if !instance.Enabled {
			continue
		}

		instanceHasOverlaps := false
		for _, period := range instance.Periods {
			if period.PeriodStatus == "notUsedOverlap" {
				if !instanceHasOverlaps {
					log.Printf("\nInstance %s (%s):", instance.InstanceId, instance.ProgramName)
					instanceHasOverlaps = true
					hasOverlaps = true
				}
				log.Printf("  Period %s (%s): %s - %s [STATUS: %s]",
					period.PeriodId, period.Name, period.StartTime, period.EndTime, period.PeriodStatus)
			}
		}
	}

	if !hasOverlaps {
		log.Println("No period overlaps detected - all periods have sufficient time gaps (≥30 minutes)")
	}
	log.Println("=============================")
}
