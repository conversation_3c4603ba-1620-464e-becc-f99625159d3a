package main

import (
	"context"
	"flag"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"program-manager/aggregatorClient"
	"program-manager/ceb"
	"program-manager/clock"
	"program-manager/configs"
	"program-manager/diurnal"
	"program-manager/io"
	"program-manager/monitoring"
	"program-manager/redis"
	"program-manager/shared"
	"program-manager/types"

	"github.com/joho/godotenv"
)

const version = "0.0.11"

// Global variables for the new modular structure
var messageBridge *shared.MessageBridgeImpl
var configManager *shared.ConfigManager
var dataProcessor *shared.DataProcessor
var validator *shared.Validator

// Helper function for graceful shutdown
func shutdown() {
	log.Println("\nShutting down...")
	if messageBridge != nil {
		messageBridge.Stop()
	}
	log.Println("Disconnected from MQTT brokers")
}

// Start cloud connector functionality in background
func startCloudConnector() {

	// Initialize configuration manager
	configManager = shared.NewConfigManager()

	// Load configuration values
	configManager.LoadConfigValues()

	// Load configurations
	if err := configManager.LoadConfigs(); err != nil {
		log.Fatalf("Error loading MQTT configs: %v", err)
	}

	configManager.PrintConfigs()

	// Initialize shared services
	dataProcessor = shared.NewDataProcessor()
	validator = shared.NewValidator()

	// Create IO module
	ioModule := io.NewIOModule(dataProcessor)
	if err := ioModule.Initialize(shared.ClientConfig); err != nil {
		log.Fatalf("Failed to initialize IO module: %v", err)
	}

	// Create Aggregator Client module
	aggregatorModule := aggregatorClient.NewAggregatorClient(dataProcessor, validator, configManager)
	if err := aggregatorModule.Initialize(shared.ServerConfig); err != nil {
		log.Fatalf("Failed to initialize Aggregator Client module: %v", err)
	}

	// Create and configure message bridge
	messageBridge = shared.NewMessageBridge()
	if err := messageBridge.ConnectModules(ioModule, aggregatorModule); err != nil {
		log.Fatalf("Failed to connect modules: %v", err)
	}

	// Start the message bridge (this starts both modules)
	if err := messageBridge.Start(); err != nil {
		log.Fatalf("Failed to start message bridge: %v", err)
	}

	// !NOTE: Wait for connections to stabilize
	time.Sleep(1 * time.Second)

	log.Printf("\nArgus Cloud Connector is running... 🚀")

	// Keep the cloud connector running in background
	select {}
}

func main() {
	log.SetFlags(0)

	log.Print("Starting control programs... v", version)

	_ = godotenv.Load()

	if shared.GetLogTimestamp() {
		log.SetFlags(log.LstdFlags)
	}

	// Parse command line flags
	var cebVerbose = flag.Bool("ceb-verbose", false, "Enable verbose logging for CEB system (shows detailed logs for 9 outputs)")
	var disableProgramManager = flag.Bool("disable-program-manager", false, "Disable Program Manager (diurnal, clock, CEB)")
	var disableCloudConnector = flag.Bool("disable-cloud-connector", false, "Disable Cloud Connector (runs by default)")
	flag.Parse()

	// Start cloud connector by default (unless disabled)
	var cloudConnectorEnabled bool
	if !*disableCloudConnector {
		go startCloudConnector()
		cloudConnectorEnabled = true
	} else {
		log.Println("Cloud Connector disabled")
	}

	// Skip program manager if disabled
	if *disableProgramManager {
		if cloudConnectorEnabled {
			log.Println("Program Manager disabled - running Cloud Connector only")
			// Set up graceful shutdown for cloud connector only
			c := make(chan os.Signal, 1)
			signal.Notify(c, os.Interrupt, syscall.SIGTERM)
			<-c
			shutdown()
			log.Println("Cloud Connector terminated gracefully")
		} else {
			log.Println("Both Program Manager and Cloud Connector disabled - nothing to run")
		}
		return
	}

	// Initialize Redis client
	redis.Initialize()
	ctx := context.Background()

	// Load program manager configuration from file
	log.Println("Loading program configuration from configs/programManager.json...")
	programManagerConfig, err := config.LoadProgramManager("configs/programManager.json")
	if err != nil {
		log.Fatalf("Failed to load program manager configuration: %v", err)
	}

	// Convert loaded configuration to program manager and statuses
	programManager := make(map[string]interface{})
	var statuses []types.ProgramStatus

	// Always enable clock program by default (not configurable)
	programManager["clock"] = types.ClockConfig{Enabled: true}
	statuses = append(statuses, types.ProgramStatus{Name: "clock", Enabled: true})
	log.Printf("✓ clock enabled (default)")

	// Process each program from configuration
	for programName, configValue := range programManagerConfig {
		var enabled bool

		// Support both boolean and nested object formats
		if enabledBool, ok := configValue.(bool); ok {
			// Simple boolean format: "programName": true/false
			enabled = enabledBool
		} else if configMap, ok := configValue.(map[string]interface{}); ok {
			// Nested object format: "programName": {"enabled": true/false}
			if enabledValue, exists := configMap["enabled"]; exists {
				if enabledBool, ok := enabledValue.(bool); ok {
					enabled = enabledBool
				}
			}
		}

		if enabled {
			// Program is enabled, set appropriate type
			switch programName {
			case "diurnalSetpoint":
				programManager[programName] = types.DiurnalSetpointConfig{Instances: []types.DiurnalInstance{}}
			case "climateEnergyBalance":
				programManager[programName] = types.CEBConfig{Enabled: true}
			case "clock":
				programManager[programName] = types.ClockConfig{Enabled: true}
			default:
				programManager[programName] = map[string]bool{"enabled": true}
			}
			statuses = append(statuses, types.ProgramStatus{Name: programName, Enabled: true})
			log.Printf("✓ %s enabled", programName)
		} else {
			statuses = append(statuses, types.ProgramStatus{Name: programName, Enabled: false})
			log.Printf("✗ %s disabled", programName)
		}
	}

	log.Printf("Loaded configuration for %d programs", len(statuses))

	// Handle diurnalSetpoint if enabled
	if _, ok := programManager["diurnalSetpoint"].(types.DiurnalSetpointConfig); ok {
		log.Println("\nLoading Enhanced DiurnalSetpoint Configuration...")

		// Load diurnal configuration - return error if file doesn't exist
		diurnalConfig, err := config.LoadDiurnalConfig("configs/diurnalConfig.json")
		if err != nil {
			log.Fatalf("Error loading diurnal configuration: %v", err)
		}

		// Validate configuration
		if err := config.ValidateLoadedConfig(diurnalConfig); err != nil {
			log.Fatalf("Invalid diurnal configuration: %v", err)
		}
		log.Println("Enhanced configuration loaded and validated successfully!")

		// Get clock state for relative time processing (if clock is enabled)
		var clockState *types.ClockState
		if _, clockEnabled := programManager["clock"].(types.ClockConfig); clockEnabled {
			// Try to load clock configuration and get current state
			if clockConfig, err := config.LoadClockConfig("configs/clockConfig.json"); err == nil {
				tempClockController := clock.NewClockController(clockConfig)
				if err := tempClockController.ProcessCycle(ctx); err == nil {
					clockState = tempClockController.GetCurrentState()
					log.Printf("Diurnal: Using clock state for relative time calculations: Dawn=%s, Dusk=%s",
						clockState.Dawn, clockState.Dusk)
				} else {
					log.Printf("Diurnal: Warning - Failed to get clock state: %v", err)
				}
			} else {
				log.Printf("Diurnal: Warning - Failed to load clock config: %v", err)
			}
		}

		if clockState == nil {
			log.Println("Diurnal: Clock not available, using default dawn/dusk times for relative time calculations")
		}

		// Process diurnal instances with clock state (includes relative time conversion)
		diurnal.ProcessInstances(ctx, diurnalConfig, clockState)

		// Print overlap status information
		diurnal.PrintPeriodOverlapStatus(diurnalConfig)

		// Start monitoring
		go monitoring.MonitorRampRates(ctx, diurnalConfig, programManager)

		// Print stored setpoints
		if err := redis.PrintAllSetpoints(ctx); err != nil {
			log.Printf("Warning: Failed to print setpoints from Redis: %v", err)
		}

		// Save configuration
		if err := config.SaveDiurnalConfig("configs/diurnalConfig.json", diurnalConfig); err != nil {
			log.Fatalf("Failed to save diurnal configuration: %v", err)
		}
	}

	// Handle clock if enabled
	var clockController *clock.ClockController
	if _, ok := programManager["clock"].(types.ClockConfig); ok {
		log.Println("\nLoading Clock Program Configuration...")

		// Load clock configuration - return error if file doesn't exist
		clockConfig, err := config.LoadClockConfig("configs/clockConfig.json")
		if err != nil {
			log.Fatalf("Failed to load Clock configuration: %v", err)
		}

		// Create and start Clock controller
		clockController = clock.NewClockController(clockConfig)

		// Start Clock processing in a separate goroutine
		go func() {
			// Run immediately on startup
			log.Printf("Clock: Running initial cycle...")
			if err := clockController.ProcessCycle(ctx); err != nil {
				log.Printf("Clock: Error in initial processing cycle: %v", err)
			}

			ticker := time.NewTicker(time.Duration(clockConfig.UpdateInterval) * time.Second)
			defer ticker.Stop()

			log.Printf("Clock: Scheduled to update every %d seconds", clockConfig.UpdateInterval)

			for {
				select {
				case <-ticker.C:
					log.Printf("Clock: Running scheduled update cycle...")
					if err := clockController.ProcessCycle(ctx); err != nil {
						log.Printf("Clock: Error in processing cycle: %v", err)
					}
				case <-ctx.Done():
					log.Printf("Clock: Shutting down...")
					return
				}
			}
		}()

		log.Println("Clock Program started successfully!")
	}

	// Handle climateEnergyBalance if enabled
	var cebController *ceb.CEBController
	if _, ok := programManager["climateEnergyBalance"].(types.CEBConfig); ok {
		log.Println("\nLoading Enhanced Climate Energy Balance Configuration...")

		// Load CEB configuration - return error if file doesn't exist
		cebConfig, err := config.LoadCEBConfig("configs/cebConfig.json")
		if err != nil {
			log.Fatalf("Failed to load CEB configuration: %v", err)
		}

		// Create and start CEB controller
		cebController = ceb.NewCEBController(cebConfig)

		// Set verbose logging based on command line flag
		cebController.SetVerboseLogging(*cebVerbose)

		// Start CEB processing in a separate goroutine
		go func() {
			ticker := time.NewTicker(5 * time.Second) // Run every 5 seconds
			defer ticker.Stop()

			for {
				select {
				case <-ticker.C:
					if err := cebController.ProcessCycle(ctx); err != nil {
						log.Printf("CEB: Error in processing cycle: %v", err)
					}
				case <-ctx.Done():
					return
				}
			}
		}()

		log.Println("Climate Energy Balance system started successfully!")
	}

	// Print enabled and disabled programs in aligned format
	maxLen := 0
	for _, s := range statuses {
		if l := len(s.Name); l > maxLen {
			maxLen = l
		}
	}
	for _, s := range statuses {
		status := "✗ disabled"
		if s.Enabled {
			status = "✓ enabled"
		}
		log.Printf("%-*s %s", maxLen, s.Name, status)
	}

	// Set up graceful shutdown for both systems
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	// Wait for interrupt signal
	<-c

	// Shutdown cloud connector if it was started
	if cloudConnectorEnabled {
		shutdown()
	}

	log.Println("Program terminated gracefully")
}
