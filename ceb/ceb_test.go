package ceb

import (
	"context"
	"testing"

	"program-manager/redis"
	"program-manager/testutils"
	"program-manager/types"
)

// TestNewCEBController tests CEB controller initialization
func TestNewCEBController(t *testing.T) {
	cebConfig := testutils.CreateEnhancedCEBConfig()

	controller := NewCEBController(cebConfig)

	if controller == nil {
		t.Fatal("NewCEBController returned nil")
	}

	if !controller.IsEnabled() {
		t.<PERSON>r("Controller should be enabled")
	}

	if controller.config.ProgramName != "climate energy balance" {
		t.<PERSON>rrorf("Expected program name 'climate energy balance', got '%s'", controller.config.ProgramName)
	}

	// Test that enhanced configuration has required sections
	if controller.config.CEBInputRedisKeys == nil {
		t.Error("Expected CEBInputRedisKeys to be configured")
	}

	if controller.config.HeatTuning == nil {
		t.Error("Expected HeatTuning to be configured")
	}

	if controller.config.VentilationTuning == nil {
		t.<PERSON>rror("Expected VentilationTuning to be configured")
	}

	// Test default verbose logging state
	if controller.verboseLogging != false {
		t.Errorf("Expected verboseLogging to be false by default, got %v", controller.verboseLogging)
	}
}

// TestCEBController_SetVerboseLogging tests the verbose logging functionality
func TestCEBController_SetVerboseLogging(t *testing.T) {
	cebConfig := testutils.CreateEnhancedCEBConfig()
	controller := NewCEBController(cebConfig)

	// Test enabling verbose logging
	controller.SetVerboseLogging(true)
	if !controller.verboseLogging {
		t.Errorf("Expected verboseLogging to be true, got %v", controller.verboseLogging)
	}

	// Test disabling verbose logging
	controller.SetVerboseLogging(false)
	if controller.verboseLogging {
		t.Errorf("Expected verboseLogging to be false, got %v", controller.verboseLogging)
	}
}

// TestCEBController_ProcessCycle tests the main processing cycle
func TestCEBController_ProcessCycle(t *testing.T) {
	// Initialize Redis for testing
	redis.Initialize()
	ctx := context.Background()

	cebConfig := testutils.CreateEnhancedCEBConfig()
	controller := NewCEBController(cebConfig)

	// Set up test data in Redis
	setupTestData(ctx, t)

	// Test processing cycle
	err := controller.ProcessCycle(ctx)
	if err != nil {
		t.Fatalf("ProcessCycle failed: %v", err)
	}

	// Verify that outputs were calculated and stored
	verifyOutputs(ctx, t, controller)
}

// TestCEBController_CalculateOutputs tests the calculation logic
func TestCEBController_CalculateOutputs(t *testing.T) {
	cebConfig := testutils.CreateEnhancedCEBConfig()
	controller := NewCEBController(cebConfig)

	// Set up test state
	controller.state = types.CEBState{
		IntegratedTemp:     20.0,
		IntegratedHumidity: 50.0,
		OutdoorTemp:        15.0,
		LightLevel:         0.0,
	}

	// Set up test setpoints
	setpoints := map[types.SetpointPurpose]float64{
		types.PurposeHeatingTarget:        15.0,
		types.PurposeCoolingTarget:        20.0,
		types.PurposeDehumidifyVentTarget: 35.0,
		types.PurposeDehumidifyHeatTarget: 75.0,
		types.PurposeMaxDehumidVent:       8.0,
		types.PurposeMaxDehumidHeat:       3.0,
	}

	// Calculate outputs using the actual method names
	controller.calculateDemands(setpoints)
	controller.applyFeedForwardAdjustments()
	controller.selectFinalRequests()

	// Verify calculations
	if controller.state.VentReqForTemperatureControl != 0.0 {
		t.Errorf("Expected VentReqForTemperatureControl to be 0.0, got %.1f", controller.state.VentReqForTemperatureControl)
	}

	if controller.state.VentReqForHumidityControl != 8.0 {
		t.Errorf("Expected VentReqForHumidityControl to be 8.0, got %.1f", controller.state.VentReqForHumidityControl)
	}

	if controller.state.HighestVentRequest != 8.0 {
		t.Errorf("Expected HighestVentRequest to be 8.0, got %.1f", controller.state.HighestVentRequest)
	}

	if controller.state.SumOfVentRequests != 8.0 {
		t.Errorf("Expected SumOfVentRequests to be 8.0, got %.1f", controller.state.SumOfVentRequests)
	}

	// Check heating calculations (approximate due to floating point)
	expectedHeatTemp := 3.333333333333333
	if abs(controller.state.HeatReqForTemperatureControl-expectedHeatTemp) > 0.01 {
		t.Errorf("Expected HeatReqForTemperatureControl to be %.1f, got %.1f", expectedHeatTemp, controller.state.HeatReqForTemperatureControl)
	}

	expectedHeatHumidity := 4.666666666666666
	if abs(controller.state.HeatReqForHumidityControl-expectedHeatHumidity) > 0.01 {
		t.Errorf("Expected HeatReqForHumidityControl to be %.1f, got %.1f", expectedHeatHumidity, controller.state.HeatReqForHumidityControl)
	}

	if abs(controller.state.HighestHeatRequest-expectedHeatHumidity) > 0.01 {
		t.Errorf("Expected HighestHeatRequest to be %.1f, got %.1f", expectedHeatHumidity, controller.state.HighestHeatRequest)
	}

	expectedSumHeat := 8.0
	if abs(controller.state.SumOfHeatRequests-expectedSumHeat) > 0.01 {
		t.Errorf("Expected SumOfHeatRequests to be %.1f, got %.1f", expectedSumHeat, controller.state.SumOfHeatRequests)
	}

	// HeatingSystemTempRequest should be calculated based on the heating demand
	// With the current test conditions, it should be around 10.9°C
	expectedHeatingSystemTemp := 10.9
	if abs(controller.state.HeatingSystemTempRequest-expectedHeatingSystemTemp) > 0.1 {
		t.Errorf("Expected HeatingSystemTempRequest to be around %.1f, got %.1f", expectedHeatingSystemTemp, controller.state.HeatingSystemTempRequest)
	}
}

// TestCEBController_GetSetpoints tests setpoint retrieval
func TestCEBController_GetSetpoints(t *testing.T) {
	// Initialize Redis for testing
	redis.Initialize()
	ctx := context.Background()

	cebConfig := testutils.CreateEnhancedCEBConfig()
	controller := NewCEBController(cebConfig)

	// Set up test setpoints in Redis
	setupTestSetpoints(ctx, t)

	// Test setpoint retrieval
	setpoints, err := controller.getSetpoints(ctx)
	if err != nil {
		t.Fatalf("getSetpoints failed: %v", err)
	}

	// Verify setpoints
	expectedSetpoints := map[types.SetpointPurpose]float64{
		types.PurposeHeatingTarget:        15.0,
		types.PurposeCoolingTarget:        20.0,
		types.PurposeDehumidifyVentTarget: 35.0,
		types.PurposeDehumidifyHeatTarget: 75.0,
		types.PurposeMaxDehumidVent:       8.0,
		types.PurposeMaxDehumidHeat:       3.0,
	}

	for purpose, expected := range expectedSetpoints {
		if actual, exists := setpoints[purpose]; !exists {
			t.Errorf("Expected setpoint %v to exist", purpose)
		} else if actual != expected {
			t.Errorf("Expected setpoint %v to be %.1f, got %.1f", purpose, expected, actual)
		}
	}
}

// TestCEBController_UpdateSensorReadings tests sensor data retrieval
func TestCEBController_UpdateSensorReadings(t *testing.T) {
	// Initialize Redis for testing
	redis.Initialize()
	ctx := context.Background()

	cebConfig := testutils.CreateEnhancedCEBConfig()
	controller := NewCEBController(cebConfig)

	// Set up test sensor data in Redis
	setupTestSensorData(ctx, t)

	// Test sensor reading update
	err := controller.updateSensorReadings(ctx)
	if err != nil {
		t.Fatalf("updateSensorReadings failed: %v", err)
	}

	// Verify sensor readings
	if controller.state.IntegratedTemp != 20.0 {
		t.Errorf("Expected IntegratedTemp to be 20.0, got %.1f", controller.state.IntegratedTemp)
	}

	if controller.state.IntegratedHumidity != 50.0 {
		t.Errorf("Expected IntegratedHumidity to be 50.0, got %.1f", controller.state.IntegratedHumidity)
	}

	if controller.state.OutdoorTemp != 15.0 {
		t.Errorf("Expected OutdoorTemp to be 15.0, got %.1f", controller.state.OutdoorTemp)
	}

	if controller.state.LightLevel != 0.0 {
		t.Errorf("Expected LightLevel to be 0.0, got %.1f", controller.state.LightLevel)
	}
}

// Helper functions for testing

func setupTestData(ctx context.Context, t *testing.T) {
	setupTestSetpoints(ctx, t)
	setupTestSensorData(ctx, t)
}

func setupTestSetpoints(ctx context.Context, t *testing.T) {
	// Set up test setpoints in Redis
	setpoints := map[string]float64{
		"hub:h1:zone:z1:instance:I1:setpoint:heatingTarget":        15.0,
		"hub:h1:zone:z1:instance:I1:setpoint:coolingTarget":        20.0,
		"hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget": 35.0,
		"hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget": 75.0,
		"hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent":       8.0,
		"hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat":       3.0,
	}

	for key, value := range setpoints {
		if err := redis.SetFloat(ctx, key, value); err != nil {
			t.Fatalf("Failed to set test setpoint %s: %v", key, err)
		}
	}
}

func setupTestSensorData(ctx context.Context, t *testing.T) {
	// Set up test sensor data in Redis
	sensors := map[string]float64{
		"hub:h1:zone:z1:sensor:temperature": 20.0,
		"hub:h1:zone:z1:sensor:humidity":    50.0,
		"hub:h1:weather:outdoorTemp":        15.0,
		"hub:h1:weather:lightLevel":         0.0,
	}

	for key, value := range sensors {
		if err := redis.SetFloat(ctx, key, value); err != nil {
			t.Fatalf("Failed to set test sensor data %s: %v", key, err)
		}
	}
}

func verifyOutputs(ctx context.Context, t *testing.T, controller *CEBController) {
	// Verify that all 9 outputs are calculated and within expected ranges
	outputs := []struct {
		name  string
		value float64
		min   float64
		max   float64
	}{
		{"VentReqForTemperatureControl", controller.state.VentReqForTemperatureControl, 0.0, 100.0},
		{"VentReqForHumidityControl", controller.state.VentReqForHumidityControl, 0.0, 100.0},
		{"HighestVentRequest", controller.state.HighestVentRequest, 0.0, 100.0},
		{"SumOfVentRequests", controller.state.SumOfVentRequests, 0.0, 200.0},
		{"HeatReqForTemperatureControl", controller.state.HeatReqForTemperatureControl, 0.0, 100.0},
		{"HeatReqForHumidityControl", controller.state.HeatReqForHumidityControl, 0.0, 100.0},
		{"HighestHeatRequest", controller.state.HighestHeatRequest, 0.0, 100.0},
		{"SumOfHeatRequests", controller.state.SumOfHeatRequests, 0.0, 200.0},
		{"HeatingSystemTempRequest", controller.state.HeatingSystemTempRequest, -50.0, 100.0},
	}

	for _, output := range outputs {
		if output.value < output.min || output.value > output.max {
			t.Errorf("Output %s value %.2f is outside expected range [%.1f, %.1f]",
				output.name, output.value, output.min, output.max)
		}
	}
}

// abs returns the absolute value of a float64
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// Benchmark tests

// BenchmarkCEBController_ProcessCycle benchmarks the main processing cycle
func BenchmarkCEBController_ProcessCycle(b *testing.B) {
	// Initialize Redis for benchmarking
	redis.Initialize()
	ctx := context.Background()

	cebConfig := testutils.CreateEnhancedCEBConfig()
	controller := NewCEBController(cebConfig)

	// Set up test data once
	setupTestData(ctx, &testing.T{})

	// Reset timer to exclude setup time
	b.ResetTimer()

	// Run the benchmark
	for i := 0; i < b.N; i++ {
		err := controller.ProcessCycle(ctx)
		if err != nil {
			b.Fatalf("ProcessCycle failed: %v", err)
		}
	}
}

// BenchmarkCEBController_CalculateOutputs benchmarks just the calculation logic
func BenchmarkCEBController_CalculateOutputs(b *testing.B) {
	cebConfig := testutils.CreateEnhancedCEBConfig()
	controller := NewCEBController(cebConfig)

	// Set up test state
	controller.state = types.CEBState{
		IntegratedTemp:     20.0,
		IntegratedHumidity: 50.0,
		OutdoorTemp:        15.0,
		LightLevel:         0.0,
	}

	// Set up test setpoints
	setpoints := map[types.SetpointPurpose]float64{
		types.PurposeHeatingTarget:        15.0,
		types.PurposeCoolingTarget:        20.0,
		types.PurposeDehumidifyVentTarget: 35.0,
		types.PurposeDehumidifyHeatTarget: 75.0,
		types.PurposeMaxDehumidVent:       8.0,
		types.PurposeMaxDehumidHeat:       3.0,
	}

	// Reset timer to exclude setup time
	b.ResetTimer()

	// Run the benchmark
	for i := 0; i < b.N; i++ {
		controller.calculateDemands(setpoints)
		controller.applyFeedForwardAdjustments()
		controller.selectFinalRequests()
	}
}
