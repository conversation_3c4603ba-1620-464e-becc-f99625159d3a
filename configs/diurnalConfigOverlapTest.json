{"instances": [{"instanceId": "1", "programName": "Overlap Detection Test", "enabled": true, "hubId": "1", "zoneId": "1", "periods": [{"periodId": "morning", "name": "Morning Period", "enabled": true, "periodStatus": "active", "startTime": "08:00", "endTime": "12:00", "activeDays": {"monday": true, "tuesday": true, "wednesday": true, "thursday": true, "friday": true, "saturday": false, "sunday": false}, "setpoints": {"heatingTarget": 22.0, "coolingTarget": 25.0}}, {"periodId": "earlyAfternoon", "name": "Early Afternoon Period", "enabled": true, "periodStatus": "active", "startTime": "12:20", "endTime": "15:00", "activeDays": {"monday": true, "tuesday": true, "wednesday": true, "thursday": true, "friday": true, "saturday": false, "sunday": false}, "setpoints": {"heatingTarget": 24.0, "coolingTarget": 26.0}}, {"periodId": "lateAfternoon", "name": "Late Afternoon Period", "enabled": true, "periodStatus": "active", "startTime": "16:00", "endTime": "18:00", "activeDays": {"monday": true, "tuesday": true, "wednesday": true, "thursday": true, "friday": true, "saturday": false, "sunday": false}, "setpoints": {"heatingTarget": 20.0, "coolingTarget": 23.0}}]}]}