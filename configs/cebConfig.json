{"programName": "climate energy balance", "enabled": true, "CEBInputRedisKeys": {"zoneTemperature": "hub:h1:io:sensorTemperature", "backupZoneTemperature": "hub:h1:io:backupSensorTemperature", "zoneHumidity": "hub:h1:io:sensorHumidity", "shadePosition": "hub:h1:zone:z1:shade:shadePosition", "coolingTarget": "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget", "heatingTarget": "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget", "dehumidifyVentilationTarget": "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget", "dehumidifyHeatingTarget": "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget", "maxLimitForDehumidifyVentilation": "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent", "maxLimitForDehumidifyHeating": "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat", "outdoorTemperature": "hub:h1:instance:I1:weather:outdoorTemperature", "lightLevelNI": "hub:h1:instance:I1:weather:lightLevelNI", "lightLevelI": "hub:h1:instance:I1:weather:lightLevelI"}, "CEBOutputRedisKeys": {"ventTempControl": "hub:h1:zone:z1:instance:I1:ceb:ventTempControl", "ventHumidityControl": "hub:h1:zone:z1:instance:I1:ceb:ventHumidityControl", "highestVentRequest": "hub:h1:zone:z1:instance:I1:ceb:highestVentRequest", "sumVentRequests": "hub:h1:zone:z1:instance:I1:ceb:sumVentRequests", "heatTempControl": "hub:h1:zone:z1:instance:I1:ceb:heatTempControl", "heatHumidityControl": "hub:h1:zone:z1:instance:I1:ceb:heatHumidityControl", "highestHeatRequest": "hub:h1:zone:z1:instance:I1:ceb:highestHeatRequest", "sumHeatRequests": "hub:h1:zone:z1:instance:I1:ceb:sumHeatRequests", "heatingSystemTempRequest": "hub:h1:zone:z1:instance:I1:ceb:heatingSystemTempRequest", "integratedTemp": "hub:h1:zone:z1:instance:I1:ceb:integratedTemp", "integratedHumidity": "hub:h1:zone:z1:instance:I1:ceb:integratedHumidity"}, "heatTuning": {"heatReqForTemperatureControl": {"heatingTarget": {"used": true, "address": "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget", "crossModuleReqTime": 30}, "zoneTemperature": {"used": true, "address": "hub:h1:io:sensorTemperature", "crossModuleReqTime": 30}, "backupZoneTemperature": {"used": true, "address": "hub:h1:io:backupSensorTemperature", "crossModuleReqTime": 30}, "heatingProportionalSpanP": 4, "heatingIntegralTimeI": 50}, "heatReqForDehumidification": {"dehumidificationLimit": {"used": false, "address": "", "crossModuleReqTime": 0}, "currentHumidity": {"used": false, "address": "", "crossModuleReqTime": 0}, "dehumidifyHeatTarget": {"used": false, "address": "", "crossModuleReqTime": 0}, "dehumidifyHeatOffset": 0, "dehumidifyHeatProportionalSpanP": 0, "dehumidifyHeatIntegralTimeI": 0}, "heatingOutdoorTemperatureEffect": {"heatingTarget": {"used": true, "address": "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget", "crossModuleReqTime": 30}, "outdoorTemperature": {"used": true, "address": "hub:h1:instance:I1:weather:outdoorTemperature", "crossModuleReqTime": 30}, "shadePosition": {"used": true, "address": "hub:h1:zone:z1:shade:shadePosition", "crossModuleReqTime": 30}, "shadeRetractedSettings": {"minTempDifference": 0, "maxTempDifference": 60, "minOutdoorEffect": 0, "maxOutdoorEffect": 40}, "shadeExtendedSettings": {"minTempDifference": 0, "maxTempDifference": 60, "minOutdoorEffect": 0, "maxOutdoorEffect": 30}}, "heatingLightEffect": {"currentLightReading": {"used": false, "address": "", "crossModuleReqTime": 0}, "integratedLightReading": {"used": false, "address": "", "crossModuleReqTime": 0}, "lightPredictionModifier": 0, "lightEffectScaling": {"minInput": 0, "maxInput": 0, "minOutput": 0, "maxOutput": 0}}, "heatingSystemRequest": {"requestNumber": 0, "useHigherOrSum": "", "minHeatingSystemTemp": 0, "maxHeatingSystemTemp": 0}}, "ventilationTuning": {"ventReqForTemperatureControl": {"coolingTarget": {"used": true, "address": "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget", "crossModuleReqTime": 30}, "zoneTemperature": {"used": true, "address": "hub:h1:io:sensorTemperature", "crossModuleReqTime": 30}, "backupZoneTemperature": {"used": true, "address": "hub:h1:io:backupSensorTemperature", "crossModuleReqTime": 30}, "coolingProportionalSpanP": 4, "coolingIntegralTimeI": 50}, "ventReqForDehumidification": {"dehumidifyVentTarget": {"used": false, "address": "", "crossModuleReqTime": 0}, "zoneHumidity": {"used": false, "address": "", "crossModuleReqTime": 0}, "maxLimitForDehumidifyVentilation": {"used": false, "address": "", "crossModuleReqTime": 0}, "ventilationDehumidifyProportionalSpanP": 0, "integralAccumulationTimeI": 0}, "ventilationOutdoorTemperatureEffect": {"coolingTarget": {"used": true, "address": "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget", "crossModuleReqTime": 30}, "outdoorTemperature": {"used": true, "address": "hub:h1:instance:I1:weather:outdoorTemperature", "crossModuleReqTime": 30}, "ventilationEffectScaling": {"minInput": 0, "maxInput": 0, "minOutput": 0, "maxOutput": 0}}, "ventilationLightEffect": {"currentLightReading": {"used": true, "address": "hub:h1:instance:I1:weather:lightLevelNI", "crossModuleReqTime": 30}, "integratedLightReading": {"used": true, "address": "hub:h1:instance:I1:weather:lightLevelI", "crossModuleReqTime": 30}, "lightPredictionModifier": 1, "lightEffectScaling": {"minInput": 0, "maxInput": 1000, "minOutput": 0, "maxOutput": 1}}}}