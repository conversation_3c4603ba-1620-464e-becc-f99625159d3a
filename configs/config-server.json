{"mqtt": {"broker": "iiop-argus-demo-mq.aatmunn.net", "port": 8883, "username": "iiop-sasken-mq", "password": "3SqP3kpF03D2HT4mSq76", "clientId": "iiop-argus-server", "protocol": "mqtts"}, "subscribeTopics": {"TopicArgusPullSensorData": "devry/chilliwack/greenhouse/pullSensorData", "TopicArgusReqControlData": "devry/chilliwack/greenhouse/reqControlData", "TopicArgusGetConfig": "devry/chilliwack/greenhouse/getConfig"}, "publishTopics": {"TopicArgusPeriodic": "devry/chilliwack/greenhouse/periodic", "TopicArgusResponseControlData": "devry/chilliwack/greenhouse/responseControlData", "TopicArgusConfig": "devry/chilliwack/greenhouse/config"}}