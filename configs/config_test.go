package config

import (
	"os"
	"testing"

	"program-manager/types"
)

// TestValidateLoadedConfig tests the enhanced configuration validation
func TestValidateLoadedConfig(t *testing.T) {
	tests := []struct {
		name      string
		config    types.DiurnalSetpointConfig
		expectErr bool
		errMsg    string
	}{
		{
			name: "Valid enhanced configuration",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "Test Diurnal",
						Enabled:     true,
						Periods: []types.Period{
							{
								PeriodId:     "1",
								Name:         "Morning",
								Enabled:      true,
								PeriodStatus: "active",
								StartTime:    "08:00",
								EndTime:      "12:00",
								ActiveDays: map[string]bool{
									"monday": true, "tuesday": true,
								},
								Setpoints: map[string]float64{
									"temperature": 22.0,
									"humidity":    60.0,
								},
							},
						},
					},
				},
			},
			expectErr: false,
		},
		{
			name: "Valid configuration with relative times",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "Dawn/Dusk Diurnal",
						Enabled:     true,
						HubId:       "1",
						ZoneId:      "1",
						Periods: []types.Period{
							{
								PeriodId:     "1",
								Name:         "Pre-Dawn",
								Enabled:      true,
								PeriodStatus: "active",
								StartTime:    "01:00 beforeDawn",
								EndTime:      "02:00 afterDawn",
								ActiveDays: map[string]bool{
									"monday": true,
								},
								Setpoints: map[string]float64{
									"temperature": 20.0,
								},
							},
						},
					},
				},
			},
			expectErr: false,
		},
		{
			name: "Empty instances",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{},
			},
			expectErr: true,
			errMsg:    "no instances defined",
		},
		{
			name: "Too many instances",
			config: types.DiurnalSetpointConfig{
				Instances: make([]types.DiurnalInstance, 9), // More than 8
			},
			expectErr: true,
			errMsg:    "too many instances",
		},
		{
			name: "Duplicate instance IDs",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "First",
						Enabled:     false, // Disabled to avoid "no periods" error
						Periods:     []types.Period{},
					},
					{
						InstanceId:  "1", // Duplicate
						ProgramName: "Second",
						Enabled:     false, // Disabled to avoid "no periods" error
						Periods:     []types.Period{},
					},
				},
			},
			expectErr: true,
			errMsg:    "duplicate instance ID found: 1",
		},
		{
			name: "Empty instance ID",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "", // Empty
						ProgramName: "Empty ID",
						Enabled:     true,
						Periods:     []types.Period{},
					},
				},
			},
			expectErr: true,
			errMsg:    "instance ID cannot be empty",
		},
		{
			name: "Missing program name",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "", // Empty
						Enabled:     true,
						Periods:     []types.Period{},
					},
				},
			},
			expectErr: true,
			errMsg:    "has no program name",
		},
		{
			name: "Enabled instance with no periods",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "No Periods",
						Enabled:     true,
						Periods:     []types.Period{}, // Empty
					},
				},
			},
			expectErr: true,
			errMsg:    "is enabled but has no periods",
		},
		{
			name: "Too many periods",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "Too Many Periods",
						Enabled:     true,
						Periods:     make([]types.Period, 9), // More than 8
					},
				},
			},
			expectErr: true,
			errMsg:    "has too many periods",
		},
		{
			name: "Duplicate period IDs",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "Duplicate Periods",
						Enabled:     true,
						Periods: []types.Period{
							{
								PeriodId:     "1",
								Name:         "First",
								Enabled:      true,
								PeriodStatus: "active",
								StartTime:    "08:00",
								EndTime:      "12:00",
								ActiveDays:   map[string]bool{"monday": true},
								Setpoints:    map[string]float64{"temp": 20.0},
							},
							{
								PeriodId:     "1", // Duplicate
								Name:         "Second",
								Enabled:      true,
								PeriodStatus: "active",
								StartTime:    "13:00",
								EndTime:      "17:00",
								ActiveDays:   map[string]bool{"monday": true},
								Setpoints:    map[string]float64{"temp": 22.0},
							},
						},
					},
				},
			},
			expectErr: true,
			errMsg:    "duplicate period ID found: 1",
		},
		{
			name: "Invalid period status",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "Invalid Status",
						Enabled:     true,
						Periods: []types.Period{
							{
								PeriodId:     "1",
								Name:         "Invalid",
								Enabled:      true,
								PeriodStatus: "invalidStatus", // Invalid
								StartTime:    "08:00",
								EndTime:      "12:00",
								ActiveDays:   map[string]bool{"monday": true},
								Setpoints:    map[string]float64{"temp": 20.0},
							},
						},
					},
				},
			},
			expectErr: true,
			errMsg:    "has invalid status: invalidStatus",
		},
		{
			name: "Invalid day name",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "Invalid Day",
						Enabled:     true,
						Periods: []types.Period{
							{
								PeriodId:     "1",
								Name:         "Invalid Day",
								Enabled:      true,
								PeriodStatus: "active",
								StartTime:    "08:00",
								EndTime:      "12:00",
								ActiveDays: map[string]bool{
									"invalidday": true, // Invalid day
								},
								Setpoints: map[string]float64{"temp": 20.0},
							},
						},
					},
				},
			},
			expectErr: true,
			errMsg:    "has invalid day: invalidday",
		},
		{
			name: "No setpoints",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "No Setpoints",
						Enabled:     true,
						Periods: []types.Period{
							{
								PeriodId:     "1",
								Name:         "Empty Setpoints",
								Enabled:      true,
								PeriodStatus: "active",
								StartTime:    "08:00",
								EndTime:      "12:00",
								ActiveDays:   map[string]bool{"monday": true},
								Setpoints:    map[string]float64{}, // Empty
							},
						},
					},
				},
			},
			expectErr: true,
			errMsg:    "has no setpoints",
		},
		{
			name: "Too many setpoints",
			config: types.DiurnalSetpointConfig{
				Instances: []types.DiurnalInstance{
					{
						InstanceId:  "1",
						ProgramName: "Too Many Setpoints",
						Enabled:     true,
						Periods: []types.Period{
							{
								PeriodId:     "1",
								Name:         "Many Setpoints",
								Enabled:      true,
								PeriodStatus: "active",
								StartTime:    "08:00",
								EndTime:      "12:00",
								ActiveDays:   map[string]bool{"monday": true},
								Setpoints: map[string]float64{
									"sp1": 1.0, "sp2": 2.0, "sp3": 3.0, "sp4": 4.0,
									"sp5": 5.0, "sp6": 6.0, "sp7": 7.0, "sp8": 8.0,
									"sp9": 9.0, // 9 setpoints (more than 8)
								},
							},
						},
					},
				},
			},
			expectErr: true,
			errMsg:    "has too many setpoints",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateLoadedConfig(tt.config)

			if tt.expectErr {
				if err == nil {
					t.Errorf("Expected error containing '%s' but got none", tt.errMsg)
					return
				}
				if tt.errMsg != "" && !contains(err.Error(), tt.errMsg) {
					t.Errorf("Expected error containing '%s', got '%s'", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

// TestLoadAndSaveDiurnalConfig tests file operations
func TestLoadAndSaveDiurnalConfig(t *testing.T) {
	// Create a test configuration
	testConfig := types.DiurnalSetpointConfig{
		Instances: []types.DiurnalInstance{
			{
				InstanceId:  "1",
				ProgramName: "Test Load/Save",
				Enabled:     true,
				HubId:       "1",
				ZoneId:      "1",
				Periods: []types.Period{
					{
						PeriodId:     "1",
						Name:         "Test Period",
						Enabled:      true,
						PeriodStatus: "active",
						StartTime:    "08:00",
						EndTime:      "12:00",
						ActiveDays: map[string]bool{
							"monday":  true,
							"tuesday": false,
						},
						Setpoints: map[string]float64{
							"temperature": 22.5,
							"humidity":    65.0,
						},
					},
				},
			},
		},
	}

	// Test file path
	testFile := "test_diurnal_config.json"

	// Clean up after test
	defer func() {
		os.Remove(testFile)
	}()

	// Test saving
	err := SaveDiurnalConfig(testFile, testConfig)
	if err != nil {
		t.Fatalf("Failed to save config: %v", err)
	}

	// Test loading
	loadedConfig, err := LoadDiurnalConfig(testFile)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify loaded config matches original
	if len(loadedConfig.Instances) != len(testConfig.Instances) {
		t.Errorf("Expected %d instances, got %d", len(testConfig.Instances), len(loadedConfig.Instances))
	}

	instance := loadedConfig.Instances[0]
	expectedInstance := testConfig.Instances[0]

	if instance.InstanceId != expectedInstance.InstanceId {
		t.Errorf("Expected InstanceId %s, got %s", expectedInstance.InstanceId, instance.InstanceId)
	}

	if instance.ProgramName != expectedInstance.ProgramName {
		t.Errorf("Expected ProgramName %s, got %s", expectedInstance.ProgramName, instance.ProgramName)
	}

	if len(instance.Periods) != len(expectedInstance.Periods) {
		t.Errorf("Expected %d periods, got %d", len(expectedInstance.Periods), len(instance.Periods))
	}

	period := instance.Periods[0]
	expectedPeriod := expectedInstance.Periods[0]

	if period.PeriodId != expectedPeriod.PeriodId {
		t.Errorf("Expected PeriodId %s, got %s", expectedPeriod.PeriodId, period.PeriodId)
	}

	if period.StartTime != expectedPeriod.StartTime {
		t.Errorf("Expected StartTime %s, got %s", expectedPeriod.StartTime, period.StartTime)
	}

	// Test setpoints
	for name, expectedValue := range expectedPeriod.Setpoints {
		if value, exists := period.Setpoints[name]; !exists {
			t.Errorf("Missing setpoint %s", name)
		} else if value != expectedValue {
			t.Errorf("Expected setpoint %s = %.1f, got %.1f", name, expectedValue, value)
		}
	}

	// Test active days
	for day, expectedActive := range expectedPeriod.ActiveDays {
		if active, exists := period.ActiveDays[day]; !exists {
			t.Errorf("Missing active day %s", day)
		} else if active != expectedActive {
			t.Errorf("Expected day %s = %v, got %v", day, expectedActive, active)
		}
	}
}

// TestLoadNonexistentFile tests loading a file that doesn't exist
func TestLoadNonexistentFile(t *testing.T) {
	_, err := LoadDiurnalConfig("nonexistent_file.json")
	if err == nil {
		t.Error("Expected error when loading nonexistent file")
	}
}

// TestSaveToInvalidPath tests saving to an invalid path
func TestSaveToInvalidPath(t *testing.T) {
	config := types.DiurnalSetpointConfig{
		Instances: []types.DiurnalInstance{},
	}

	err := SaveDiurnalConfig("/invalid/path/config.json", config)
	if err == nil {
		t.Error("Expected error when saving to invalid path")
	}
}

// Helper function to check if string contains substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && findSubstring(s, substr)))
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
