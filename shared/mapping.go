package shared

// Helper functions to get mappings from centralized mapping config

// GetCardsToZonesMapping returns the card to zone mapping
func GetCardsToZonesMapping() map[string]string {
	if mappingConfig.CardZones != nil {
		return mappingConfig.CardZones
	}
	// Fallback to default mapping for backward compatibility
	return map[string]string{
		"CD1": "north_house",
		"CD2": "north_house",
		"CD3": "north_house",
		"CD4": "north_house",
		"CD5": "north_house",
		"CD6": "south_house",
		"CD7": "south_house",
		"CD8": "south_house",
		"CD9": "propagation_house",
	}
}

// GetZoneIdToNamesMapping returns the zone ID to name mapping
func GetZoneIdToNamesMapping() map[string]string {
	if mappingConfig.ZoneIdNames != nil {
		return mappingConfig.ZoneIdNames
	}
	// Fallback to default mapping for backward compatibility
	return map[string]string{
		"north_house":       "North House",
		"south_house":       "South House",
		"propagation_house": "Propagation House",
	}
}

// GetZoneIdToHubsMapping returns the zone ID to hub mapping
func GetZoneIdToHubsMapping() map[string]string {
	if mappingConfig.ZoneIdHubs != nil {
		return mappingConfig.ZoneIdHubs
	}
	// Fallback to default mapping if not configured
	return map[string]string{
		"north_house":       "hub1",
		"south_house":       "hub1",
		"propagation_house": "hub1",
	}
}

// GetCardsForZone returns all cards for a given zone
func GetCardsForZone(zoneId string) []string {
	cardsToZonesMapping := GetCardsToZonesMapping()
	var cards []string

	for cardId, cardZone := range cardsToZonesMapping {
		if cardZone == zoneId {
			cards = append(cards, cardId)
		}
	}

	return cards
}
