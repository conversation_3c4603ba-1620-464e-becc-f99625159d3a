package shared

import (
	"crypto/tls"
	"fmt"
	"log"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// Retry configuration
const (
	maxRetries     = 5
	initialBackoff = 1 * time.Second
	maxBackoff     = 5 * time.Second
)

// MQTTClientWrapper wraps MQTT client functionality
type MQTTClientWrapper struct {
	client         mqtt.Client
	config         Config
	name           string
	connectHandler mqtt.OnConnectHandler
	lostHandler    mqtt.ConnectionLostHandler
}

// NewMQTTClientWrapper creates a new MQTT client wrapper
func NewMQTTClientWrapper(config Config, clientID string, messageHandler mqtt.MessageHandler, connectHandler mqtt.OnConnectHandler, lostHandler mqtt.ConnectionLostHandler, name string) *MQTTClientWrapper {
	wrapper := &MQTTClientWrapper{
		config:         config,
		name:           name,
		connectHandler: connectHandler,
		lostHandler:    lostHandler,
	}

	wrapper.createClient(clientID, messageHandler)
	return wrapper
}

// createClient creates the MQTT client with the given parameters
func (mcw *MQTTClientWrapper) createClient(clientID string, messageHandler mqtt.MessageHandler) {
	opts := mqtt.NewClientOptions()
	brokerURL := fmt.Sprintf("%s://%s:%d", mcw.config.MQTT.Protocol, mcw.config.MQTT.Broker, mcw.config.MQTT.Port)

	opts.AddBroker(brokerURL)
	opts.SetUsername(mcw.config.MQTT.Username)
	opts.SetPassword(mcw.config.MQTT.Password)
	opts.SetClientID(clientID)
	opts.SetTLSConfig(&tls.Config{InsecureSkipVerify: true})
	if messageHandler != nil {
		opts.SetDefaultPublishHandler(messageHandler)
	}
	opts.OnConnect = mcw.connectHandler
	opts.OnConnectionLost = mcw.lostHandler

	// Common connection settings
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetConnectTimeout(5 * time.Second)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(10 * time.Second)

	mcw.client = mqtt.NewClient(opts)
}

// SetMessageHandler sets the message handler for the client
func (mcw *MQTTClientWrapper) SetMessageHandler(handler mqtt.MessageHandler, clientID string) {
	// Recreate the client with the new handler
	mcw.createClient(clientID, handler)
}

// Connect connects to the MQTT broker with retry logic
func (mcw *MQTTClientWrapper) Connect() error {
	backoff := initialBackoff
	for attempt := 1; attempt <= maxRetries; attempt++ {
		log.Printf("Connecting to MQTT broker (%s) - attempt %d/%d...\n", mcw.name, attempt, maxRetries)

		if token := mcw.client.Connect(); token.Wait() && token.Error() != nil {
			log.Printf("Connection failed: %v\n", token.Error())

			if attempt < maxRetries {
				log.Printf("Retrying in %v...\n", backoff)
				time.Sleep(backoff)
				backoff = min(backoff*2, maxBackoff)
				continue
			}
			return fmt.Errorf("failed to connect after %d attempts: %v", maxRetries, token.Error())
		}

		log.Printf("Successfully connected to MQTT broker (%s)\n", mcw.name)
		return nil
	}
	return fmt.Errorf("failed to connect after %d attempts", maxRetries)
}

// SubscribeToTopics subscribes to all topics in the config
func (mcw *MQTTClientWrapper) SubscribeToTopics() {
	log.Printf("\nSubscribing to %d %s topics...", len(mcw.config.SubscribeTopics), mcw.name)

	// First, list all topics that will be subscribed to
	for _, topic := range mcw.config.SubscribeTopics {
		log.Printf("  - %s", topic)
	}

	// Then actually subscribe to each topic
	for topicName, topic := range mcw.config.SubscribeTopics {
		if token := mcw.client.Subscribe(topic, GetQoS(), nil); token.Wait() && token.Error() != nil {
			log.Printf("Failed to subscribe to %s topic '%s' (%s): %v", mcw.name, topicName, topic, token.Error())
		}
	}
}

// UnsubscribeFromTopics unsubscribes from all topics
func (mcw *MQTTClientWrapper) UnsubscribeFromTopics() {
	log.Printf("Unsubscribing from %s topics...\n", mcw.name)

	for _, topic := range mcw.config.SubscribeTopics {
		if token := mcw.client.Unsubscribe(topic); token.Wait() && token.Error() != nil {
			log.Printf("Failed to unsubscribe from %s topic '%s': %v", mcw.name, topic, token.Error())
		}
	}
}

// Disconnect disconnects from the MQTT broker
func (mcw *MQTTClientWrapper) Disconnect() {
	mcw.client.Disconnect(250)
}

// GetClient returns the underlying MQTT client
func (mcw *MQTTClientWrapper) GetClient() mqtt.Client {
	return mcw.client
}

// Publish publishes a message to a topic
func (mcw *MQTTClientWrapper) Publish(topic string, payload interface{}) error {
	if token := mcw.client.Publish(topic, GetQoS(), false, payload); token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to publish to %s: %v", topic, token.Error())
	}
	return nil
}

// Helper function for min
func min(a, b time.Duration) time.Duration {
	if a < b {
		return a
	}
	return b
}

// PrintSubscribedTopics prints all subscribed topics
func PrintSubscribedTopics(serverWrapper, clientWrapper *MQTTClientWrapper) {
	log.Printf("\nMQTT clients are running and listening for messages...\n")
	for _, wrapper := range []*MQTTClientWrapper{serverWrapper, clientWrapper} {
		log.Printf("%s subscribed topics:\n", wrapper.name)
		for topicName, topic := range wrapper.config.SubscribeTopics {
			log.Printf("  - %s: %s\n", topicName, topic)
		}
		log.Println()
	}
}
