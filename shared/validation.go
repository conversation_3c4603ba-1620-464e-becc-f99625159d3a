package shared

import (
	"fmt"
	"log"
	"strings"
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error for %s: %s", e.Field, e.Message)
}

// Validator handles validation operations
type Validator struct{}

// NewValidator creates a new validator
func NewValidator() *Validator {
	return &Validator{}
}

// ValidateControlRequest validates a control request
func (v *Validator) ValidateControlRequest(req ControlRequest) error {
	// Validation 1: Check if cardId belongs to the requested zoneId
	cardsToZonesMapping := GetCardsToZonesMapping()
	expectedZoneID, cardExists := cardsToZonesMapping[req.CardID]
	if !cardExists {
		log.Printf("Ignoring control request: cardId '%s' not found in mapping", req.CardID)
		return ValidationError{
			Field:   "cardId",
			Message: fmt.Sprintf("cardId '%s' not found in mapping", req.CardID),
		}
	}
	if expectedZoneID != req.ZoneID {
		log.Printf("Ignoring control request: cardId '%s' does not belongs to zone '%s'",
			req.CardID, req.ZoneID)
		return ValidationError{
			Field:   "zoneId",
			Message: fmt.Sprintf("cardId '%s' does not belong to zone '%s'", req.CardID, req.ZoneID),
		}
	}

	// Validation 2: Check if zoneId belongs to the requested hubId
	zoneIdToHubsMapping := GetZoneIdToHubsMapping()
	expectedHubID, zoneExists := zoneIdToHubsMapping[req.ZoneID]
	if !zoneExists {
		log.Printf("Ignoring control request: zoneId '%s' not found in hub mapping", req.ZoneID)
		return ValidationError{
			Field:   "zoneId",
			Message: fmt.Sprintf("zoneId '%s' not found in hub mapping", req.ZoneID),
		}
	}
	if expectedHubID != req.HubID {
		log.Printf("Ignoring control request: zoneId '%s' does not belong to hub '%s'",
			req.ZoneID, req.HubID)
		return ValidationError{
			Field:   "hubId",
			Message: fmt.Sprintf("zoneId '%s' does not belong to hub '%s'", req.ZoneID, req.HubID),
		}
	}

	log.Printf("✓ Validation passed: cardId '%s' belongs to zoneId '%s' and zoneId belongs to hubId '%s'",
		req.CardID, req.ZoneID, req.HubID)

	return nil
}

// ValidateReadRequest validates a read request
func (v *Validator) ValidateReadRequest(req ReadRequest) error {
	// Basic validation for read requests
	if req.RequestType == "" {
		return ValidationError{
			Field:   "requestType",
			Message: "requestType cannot be empty",
		}
	}

	// Validate request type
	validTypes := []string{"all", "zone", "card", "channel", "cards"}
	isValid := false
	for _, validType := range validTypes {
		if req.RequestType == validType {
			isValid = true
			break
		}
	}

	if !isValid {
		return ValidationError{
			Field:   "requestType",
			Message: fmt.Sprintf("invalid requestType '%s'", req.RequestType),
		}
	}

	return nil
}

// ValidateWriteRequest validates a write request
func (v *Validator) ValidateWriteRequest(req WriteRequest) error {
	// Basic validation for write requests
	if len(req.Cards) == 0 {
		return ValidationError{
			Field:   "cards",
			Message: "cards array cannot be empty",
		}
	}

	for i, card := range req.Cards {
		if card.CardID == "" {
			return ValidationError{
				Field:   fmt.Sprintf("cards[%d].cardId", i),
				Message: "cardId cannot be empty",
			}
		}

		if len(card.Channels) == 0 {
			return ValidationError{
				Field:   fmt.Sprintf("cards[%d].channels", i),
				Message: "channels array cannot be empty",
			}
		}

		for j, channel := range card.Channels {
			if channel.ChannelID == "" {
				return ValidationError{
					Field:   fmt.Sprintf("cards[%d].channels[%d].channelId", i, j),
					Message: "channelId cannot be empty",
				}
			}
		}
	}

	return nil
}

// ValidateZoneRequest validates a zone-specific read request
func (v *Validator) ValidateZoneRequest(req ReadRequest) error {
	// Basic validation first
	if err := v.ValidateReadRequest(req); err != nil {
		return err
	}

	// Check if hubId is provided
	if req.HubID == "" {
		return ValidationError{
			Field:   "hubId",
			Message: "hubId is required for zone requests",
		}
	}

	// Check if value (zoneId) is provided
	if req.Value == "" {
		return ValidationError{
			Field:   "value",
			Message: "zoneId is required for zone requests",
		}
	}

	// Check if zoneId exists in mapping
	zoneIdToHubsMapping := GetZoneIdToHubsMapping()
	expectedHubID, zoneExists := zoneIdToHubsMapping[req.Value]
	if !zoneExists {
		log.Printf("Ignoring zone request: zoneId '%s' not found in hub mapping", req.Value)
		return ValidationError{
			Field:   "value",
			Message: fmt.Sprintf("zoneId '%s' not found in hub mapping", req.Value),
		}
	}

	// Check if hubId matches the mapping
	if expectedHubID != req.HubID {
		log.Printf("Ignoring zone request: zoneId '%s' does not belong to hub '%s'", req.Value, req.HubID)
		return ValidationError{
			Field:   "hubId",
			Message: fmt.Sprintf("zoneId '%s' does not belong to hub '%s'", req.Value, req.HubID),
		}
	}

	log.Printf("✓ Zone validation passed: zoneId '%s' belongs to hubId '%s'", req.Value, req.HubID)
	return nil
}

// ValidateCardRequest validates a card-specific read request
func (v *Validator) ValidateCardRequest(req ReadRequest) error {
	// Basic validation first
	if err := v.ValidateReadRequest(req); err != nil {
		return err
	}

	// Check if hubId is provided
	if req.HubID == "" {
		return ValidationError{
			Field:   "hubId",
			Message: "hubId is required for card requests",
		}
	}

	// Check if value (zone/cardId) is provided
	if req.Value == "" {
		return ValidationError{
			Field:   "value",
			Message: "zone/cardId is required for card requests",
		}
	}

	// Parse zone/cardId format
	parts := strings.Split(req.Value, "/")
	if len(parts) != 2 {
		return ValidationError{
			Field:   "value",
			Message: "value must be in format 'zoneId/cardId' for card requests",
		}
	}

	zoneId := parts[0]
	cardId := parts[1]

	// Check if zoneId exists in mapping and belongs to hubId
	zoneIdToHubsMapping := GetZoneIdToHubsMapping()
	expectedHubID, zoneExists := zoneIdToHubsMapping[zoneId]
	if !zoneExists {
		log.Printf("Ignoring card request: zoneId '%s' not found in hub mapping", zoneId)
		return ValidationError{
			Field:   "value",
			Message: fmt.Sprintf("zoneId '%s' not found in hub mapping", zoneId),
		}
	}

	if expectedHubID != req.HubID {
		log.Printf("Ignoring card request: zoneId '%s' does not belong to hub '%s'", zoneId, req.HubID)
		return ValidationError{
			Field:   "hubId",
			Message: fmt.Sprintf("zoneId '%s' does not belong to hub '%s'", zoneId, req.HubID),
		}
	}

	// Check if cardId belongs to the zoneId
	cardsToZonesMapping := GetCardsToZonesMapping()
	expectedZoneID, cardExists := cardsToZonesMapping[cardId]
	if !cardExists {
		log.Printf("Ignoring card request: cardId '%s' not found in mapping", cardId)
		return ValidationError{
			Field:   "value",
			Message: fmt.Sprintf("cardId '%s' not found in mapping", cardId),
		}
	}

	if expectedZoneID != zoneId {
		log.Printf("Ignoring card request: cardId '%s' does not belong to zone '%s'", cardId, zoneId)
		return ValidationError{
			Field:   "value",
			Message: fmt.Sprintf("cardId '%s' does not belong to zone '%s'", cardId, zoneId),
		}
	}

	log.Printf("✓ Card validation passed: cardId '%s' belongs to zoneId '%s' and zoneId belongs to hubId '%s'", cardId, zoneId, req.HubID)
	return nil
}

// ValidateChannelRequest validates a channel-specific read request
func (v *Validator) ValidateChannelRequest(req ReadRequest) error {
	// Basic validation first
	if err := v.ValidateReadRequest(req); err != nil {
		return err
	}

	// Check if hubId is provided
	if req.HubID == "" {
		return ValidationError{
			Field:   "hubId",
			Message: "hubId is required for channel requests",
		}
	}

	// Check if value (zone/cardId/channelId) is provided
	if req.Value == "" {
		return ValidationError{
			Field:   "value",
			Message: "zone/cardId/channelId is required for channel requests",
		}
	}

	// Parse zone/cardId/channelId format
	parts := strings.Split(req.Value, "/")
	if len(parts) != 3 {
		return ValidationError{
			Field:   "value",
			Message: "value must be in format 'zoneId/cardId/channelId' for channel requests",
		}
	}

	zoneId := parts[0]
	cardId := parts[1]
	// channelId := parts[2] // We don't need to validate channelId against mapping

	// Check if zoneId exists in mapping and belongs to hubId
	zoneIdToHubsMapping := GetZoneIdToHubsMapping()
	expectedHubID, zoneExists := zoneIdToHubsMapping[zoneId]
	if !zoneExists {
		log.Printf("Ignoring channel request: zoneId '%s' not found in hub mapping", zoneId)
		return ValidationError{
			Field:   "value",
			Message: fmt.Sprintf("zoneId '%s' not found in hub mapping", zoneId),
		}
	}

	if expectedHubID != req.HubID {
		log.Printf("Ignoring channel request: zoneId '%s' does not belong to hub '%s'", zoneId, req.HubID)
		return ValidationError{
			Field:   "hubId",
			Message: fmt.Sprintf("zoneId '%s' does not belong to hub '%s'", zoneId, req.HubID),
		}
	}

	// Check if cardId belongs to the zoneId
	cardsToZonesMapping := GetCardsToZonesMapping()
	expectedZoneID, cardExists := cardsToZonesMapping[cardId]
	if !cardExists {
		log.Printf("Ignoring channel request: cardId '%s' not found in mapping", cardId)
		return ValidationError{
			Field:   "value",
			Message: fmt.Sprintf("cardId '%s' not found in mapping", cardId),
		}
	}

	if expectedZoneID != zoneId {
		log.Printf("Ignoring channel request: cardId '%s' does not belong to zone '%s'", cardId, zoneId)
		return ValidationError{
			Field:   "value",
			Message: fmt.Sprintf("cardId '%s' does not belong to zone '%s'", cardId, zoneId),
		}
	}

	log.Printf("✓ Channel validation passed: cardId '%s' belongs to zoneId '%s' and zoneId belongs to hubId '%s'", cardId, zoneId, req.HubID)
	return nil
}
