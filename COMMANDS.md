# Program Manager Commands Reference

This is the primary commands reference for the Program Manager system. All available commands and usage instructions are documented here.

## Quick Start Commands

### Build Everything

```bash
make all                    # Build all programs and tools
```

### Run Main Program

```bash
./bin/program-manager       # Interactive program selection menu
```

### Weather Simulation

```bash
./scripts/run_weather_simulator.sh    # Start weather data simulation
make run-weather-sim                  # Alternative weather simulator start
make test-weather                     # Test weather data availability
make monitor-ceb                      # Monitor CEB weather integration
```

### Zone Sensor Simulation

```bash
make run-zone-sensors                 # Start zone sensor simulation (comfortable)
make run-zone-sensors-hot             # Hot scenario (26°C, 40% humidity)
make run-zone-sensors-cold            # Cold scenario (18°C, 65% humidity)
make run-zone-sensors-humid           # Humid scenario (22°C, 75% humidity)
./scripts/run_zone_sensor_simulator.sh [scenario]  # Manual with scenario
```

### CEB Testing

```bash
make run-ceb-test          # Interactive CEB test with custom inputs
./scripts/demo_ceb_test.sh # Complete CEB test demo
```

## Build Commands

### Individual Programs

```bash
make program-manager       # Build main program
make test-ceb-interactive # Build interactive CEB test
make test-clock           # Build clock test utility
make weather-simulator    # Build weather simulation tools
make zone-sensor-simulator # Build zone sensor simulation
```

### Clean Build

```bash
make clean                # Remove all build artifacts
```

## Program Manager Options

When you run `./bin/program-manager`, you get these options:

1. **diurnalSetpoint** - Manage time-based setpoint schedules
2. **climateEnergyBalance** - Run CEB system for HVAC control
3. **weatherMonitoring** - Monitor weather data (if available)

**Clock Program** starts automatically and provides time/astronomical data.

## Weather Simulation Commands

### Start Weather Simulator

```bash
# Method 1: Using script (recommended)
./scripts/run_weather_simulator.sh

# Method 2: Using make
make run-weather-sim

# Method 3: Manual
go build -o bin/weather_simulator scripts/weather_simulator.go
./bin/weather_simulator
```

### Test Weather Data

```bash
make test-weather          # Check if weather data is available
go run scripts/test_weather_data.go  # Manual test
```

### Monitor Weather Integration

```bash
make monitor-ceb           # Monitor weather data flow to CEB
```

### Weather Data Topics (Redis)

-   `hub:h1:instance:I1:weather:outdoorTemperature`
-   `hub:h1:instance:I1:weather:lightLevelNI`
-   `hub:h1:instance:I1:weather:lightLevelI`

## Zone Sensor Simulation Commands

### Start Zone Sensor Simulation

```bash
# Method 1: Using make (recommended)
make run-zone-sensors                 # Comfortable scenario (default)
make run-zone-sensors-hot             # Hot day scenario
make run-zone-sensors-cold            # Cold day scenario
make run-zone-sensors-humid           # High humidity scenario

# Method 2: Using script directly
./scripts/run_zone_sensor_simulator.sh comfortable
./scripts/run_zone_sensor_simulator.sh hot
./scripts/run_zone_sensor_simulator.sh cold
./scripts/run_zone_sensor_simulator.sh humid

# Method 3: Manual build and run
make zone-sensor-simulator
./bin/zone_sensor_simulator [scenario]
```

### Zone Sensor Scenarios

1. **Comfortable** - Normal indoor conditions (22°C, 50% humidity)
2. **Hot** - Hot conditions requiring cooling (26°C, 40% humidity)
3. **Cold** - Cold conditions requiring heating (18°C, 65% humidity)
4. **Humid** - High humidity requiring dehumidification (22°C, 75% humidity)

### Zone Sensor Data Topics (Redis)

-   `hub:h1:io:sensorTemperature` (Zone Temperature)
-   `hub:h1:io:backupSensorTemperature` (Backup Zone Temperature)
-   `hub:h1:io:sensorHumidity` (Zone Humidity)

### Zone Sensor Features

-   **Realistic Daily Cycles** - Temperature and humidity follow natural patterns
-   **Sensor Redundancy** - Backup temperature sensor with slight variations
-   **Random Variations** - Realistic sensor noise and fluctuations
-   **Scenario-Based** - Different base conditions for testing various CEB responses
-   **Real-Time Updates** - Data updated every 5 seconds with TTL of 60 minutes

## CEB (Climate Energy Balance) Commands

### Interactive CEB Testing

```bash
# Method 1: Using make (recommended)
make run-ceb-test

# Method 2: Using demo script
./scripts/demo_ceb_test.sh

# Method 3: Manual
make test-ceb-interactive
./bin/test-ceb-interactive
```

### CEB Test Scenarios

1. **Custom Input Values** - Enter your own sensor and setpoint values
2. **Hot Day Preset** - High temperatures requiring cooling
3. **Cold Day Preset** - Low temperatures requiring heating
4. **High Humidity Preset** - High humidity requiring dehumidification

### CEB Input Parameters (11 values)

-   Zone Temperature (°C)
-   Zone Humidity (%)
-   Outdoor Temperature (°C)
-   Light Level (lux)
-   Shade Position (%)
-   Heating Target (°C)
-   Cooling Target (°C)
-   Dehumidify Ventilation Target (%)
-   Dehumidify Heating Target (%)
-   Max Dehumidify Ventilation (%)
-   Max Dehumidify Heating (%)

### CEB Outputs (9 values)

1. Ventilation Required for Temperature Control (%)
2. Ventilation Required for Humidity Control (%)
3. Highest Ventilation Request (%)
4. Sum of Ventilation Requests (%)
5. Heating Required for Temperature Control (%)
6. Heating Required for Humidity Control (%)
7. Highest Heating Request (%)
8. Sum of Heating Requests (%)
9. Current Temperature Request to Heating System (°C)

## Clock Program Commands

### Test Clock Data

```bash
go run test/test_clock.go   # Test clock data availability
```

### Clock Data Topics (Redis)

-   `hub:1:clock:currentTime`
-   `hub:1:clock:date`
-   `hub:1:clock:dayOfWeek`
-   `hub:1:clock:dawn` (sunrise time)
-   `hub:1:clock:dusk` (sunset time)
-   `hub:1:clock:isDaytime`

**Note**: Clock uses sunrise/sunset times (not civil twilight) for dawn/dusk values.

## Diurnal Program Commands

### Test Diurnal Data

```bash
# Diurnal testing is integrated into the main program
./bin/program-manager       # Select option 1: diurnalSetpoint
```

### Diurnal Features

-   Time-based setpoint scheduling
-   Dawn/dusk relative timing
-   Smooth ramping between setpoints
-   Multiple setpoint support (up to 8)

## Testing Commands

### Unit Tests

```bash
make test                  # Run all unit tests
go test ./redis -v        # Test Redis functionality
go test ./clock -v        # Test clock functionality
go test ./diurnal -v      # Test diurnal functionality
go test ./ceb -v          # Test CEB functionality
```

### Integration Tests

```bash
make run-ceb-test            # Interactive CEB testing
go run test/test_clock.go    # Test clock system
```

## Redis Commands

### Check Redis Status

```bash
redis-cli ping             # Check if Redis is running
redis-cli                  # Open Redis CLI
```

### Start Redis (if not running)

```bash
# macOS
brew services start redis

# Linux
sudo systemctl start redis

# Manual
redis-server
```

### View Data in Redis

```bash
# List all keys
redis-cli KEYS "*"

# Get specific values
redis-cli GET "hub:1:clock:dawn"
redis-cli GET "hub:h1:instance:I1:weather:outdoorTemperature"
redis-cli GET "hub:h1:zone:z1:instance:I1:ceb:ventTempControl"
```

## Complete Workflow Examples

### 1. CEB System with Weather Simulation

```bash
# Terminal 1: Start weather simulation
./scripts/run_weather_simulator.sh

# Terminal 2: Run CEB system
./bin/program-manager
# Select option 2: climateEnergyBalance

# Terminal 3: Monitor integration
make monitor-ceb
```

### 1b. CEB System with Zone Sensor Simulation

```bash
# Terminal 1: Start zone sensor simulation
make run-zone-sensors-hot

# Terminal 2: Run CEB system
./bin/program-manager
# Select option 2: climateEnergyBalance

# Terminal 3: Monitor CEB outputs
redis-cli KEYS "*ceb*"
```

### 1c. Complete CEB Testing with All Simulations

```bash
# Terminal 1: Weather simulation
./scripts/run_weather_simulator.sh

# Terminal 2: Zone sensor simulation
make run-zone-sensors-comfortable

# Terminal 3: CEB system
./bin/program-manager
# Select option 2: climateEnergyBalance

# Terminal 4: Monitor everything
make monitor-ceb
```

### 2. Test CEB with Custom Scenarios

```bash
# Run interactive CEB test
make run-ceb-test

# Try different scenarios:
# - Hot Day: High temp, need cooling
# - Cold Day: Low temp, need heating
# - High Humidity: Need dehumidification
# - Custom: Enter your own values
```

### 3. Complete System Test

```bash
# 1. Build everything
make all

# 2. Start weather simulation
./scripts/run_weather_simulator.sh

# 3. Run diurnal program (in another terminal)
./bin/program-manager
# Select option 1: diurnalSetpoint

# 4. Run CEB system (in another terminal)
./bin/program-manager
# Select option 2: climateEnergyBalance

# 5. Monitor everything (in another terminal)
make monitor-ceb
```

## Configuration Files

### Main Configuration

-   `configs/clockConfig.json` - Clock program settings
-   `configs/cebConfigEnhanced.json` - CEB system configuration
-   `configs/diurnalConfigEnhanced.json` - Diurnal program configuration

### Coordinates Used

All programs use: **17.407104033722273, 78.38716849147556** (Hyderabad, India)

## Troubleshooting Commands

### Check System Status

```bash
# Check if Redis is running
redis-cli ping

# Check if programs are built
ls -la bin/

# Check Redis data
redis-cli KEYS "*"
```

### Clean and Rebuild

```bash
make clean
make all
```

### View Logs

```bash
# Weather simulator logs (if running in background)
tail -f weather_sim.log

# Monitor logs (if running in background)
tail -f monitor.log
```

## File Structure

```
├── bin/                   # Built executables
├── ceb/                   # Climate Energy Balance module
├── clock/                 # Clock and astronomical calculations
├── configs/              # Configuration files
├── diurnal/              # Diurnal setpoint management
├── monitoring/           # Monitoring and ramp rate calculations
├── program/              # Program selection logic
├── redis/                # Redis operations
├── scripts/              # Weather simulation and utility scripts
├── test/                 # Test programs
├── types/                # Data structures and type definitions
├── docs/                 # Essential documentation
├── Makefile             # Build commands
├── CHANGELOG.md         # Version history and changes
├── COMMANDS.md          # This file (primary reference)
└── README.md            # Quick start guide
```

## Quick Reference

| Task               | Command                              |
| ------------------ | ------------------------------------ |
| Build all          | `make all`                           |
| Run main program   | `./bin/program-manager`              |
| Weather simulation | `./scripts/run_weather_simulator.sh` |
| Zone sensors       | `make run-zone-sensors`              |
| CEB testing        | `make run-ceb-test`                  |
| Monitor CEB        | `make monitor-ceb`                   |
| Test weather       | `make test-weather`                  |
| Clean build        | `make clean`                         |
| Run tests          | `make test`                          |

For detailed usage of any command, refer to the specific sections above.
