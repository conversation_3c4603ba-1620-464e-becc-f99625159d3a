# MQTT Message flow (client <-> server)

## Overview

This document outlines the high-level plan for a Go program that acts as a bridge between the **Cloud Platform (server)** and **IO Firmware (client/device)** via an MQTT broker. The program handles subscription and publishing of MQTT topics, routes messages, and uses SSL/TLS with username/password authentication. Configuration parameters and topics are defined in a `config.json` file. The program and IO Firmware run on the same device, allowing direct file access to the IO Firmware configuration in `firmware-config.json`.

## Topic Configuration

The configuration is split into two files:

1. `configs/config-server.json` - Contains topics for communication with the Cloud Platform
2. `configs/config-client.json` - Contains topics for communication with the IO Firmware

The program acts as a bridge between these two configurations, routing messages between the Cloud Platform and IO Firmware topics.

## Message Flows

1. **Configuration Flow**:

    - **Device Setup**: Program reads IO Firmware configuration from `configs/firmware-config.json` in the following format:

        ```json
        <!-- example of firmware-config.json -->
        {
            "zoneName": "zone_a",
            "deviceId": "**********",
            "mqttConf": {
                "host": "127.0.0.1",
                "port": 1883,
                "username": "admin",
                "password": "admin"
            },
            "mqttTopics": {
                "readReqTopic": "readData/req",
                "readResTopic": "readData/res",
                "writeReqTopic": "writeData/req",
                "writeResTopic": "writeData/res",
                "periodicResTopic": "readData/periodic/res",
                "periodicityInterval": 5000
            },
            "retry": {
                "count": 3,
                "waitTime": 5000
            },
            "ports": [
                {
                    "baudRate": 115200,
                    "parity": "NONE",
                    "cards": [
                        {
                            "cardId": "CD1",
                            "modbusAddr": "0x23",
                            "channels": [
                                {
                                    "channelId": "CH1",
                                    "channelName": "Unit Heater",
                                    "type": "output",
                                    "signalType": "digital",
                                    "value": {
                                        "address": "0x00",
                                        "bytes": 4
                                    }
                                },
                                {
                                    "channelId": "CH2",
                                    "channelName": "Vent Fan",
                                    "type": "output",
                                    "signalType": "digital",
                                    "value": {
                                        "address": "0x01",
                                        "bytes": 4
                                    }
                                },
                                {
                                    "channelId": "CH4",
                                    "channelName": "Temperature",
                                    "type": "input",
                                    "signalType": "analog",
                                    "value": {
                                        "address": "0x01",
                                        "bytes": 4
                                    }
                                },
                                {
                                    "channelId": "CH5",
                                    "channelName": "Humidity",
                                    "type": "input",
                                    "signalType": "analog",
                                    "value": {
                                        "address": "0x02",
                                        "bytes": 4
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        ```

        Program transforms this into the Cloud Platform format and publishes to `argus/bangalore/aatmunnOffice/config` with configured QoS

        > Note: type in client side is `input/output` but in server side it is `read/write`

        ```json
        {
            "updatedAt": 1750677566762,
            "zones": [
                {
                    "hubId": "hub1", // added from server config
                    "zoneId": "zone_a", // from client config
                    "zoneName": "zone A",
                    "cards": [
                        {
                            "cardId": "CD1",
                            "cardName": "CD1",
                            "channels": [
                                {
                                    "channelId": "CH1",
                                    "channelName": "Unit Heater",
                                    "type": "write",
                                    "signalType": "digital",
                                    "control": null
                                },
                                {
                                    "channelId": "CH2",
                                    "channelName": "Vent Fan",
                                    "type": "write",
                                    "signalType": "digital",
                                    "control": null
                                },
                                {
                                    "channelId": "CH4",
                                    "channelName": "Temperature",
                                    "type": "read",
                                    "signalType": "analog",
                                    "control": null
                                },
                                {
                                    "channelId": "CH5",
                                    "channelName": "Humidity",
                                    "type": "read",
                                    "signalType": "analog",
                                    "control": null
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        ```

        Cloud Platform subscribes to apply configurations. Program stores config locally and writes updates to `firmware-config.json`.

    - **Get Config**: Cloud Platform publishes to `argus/bangalore/aatmunnOffice/getConfig`:

        ```json
        {
            "requestType": "all/zone",
            "value": "*/${zoneId}"
        }
        ```

        Program reads the stored config from `firmware-config.json`, transforms it, and publishes to `argus/bangalore/aatmunnOffice/config` using the above Cloud Platform format with configured QoS.
        If requestType is `all`, the program includes all zones. If it is `zone`, the program filters the config to include only the specified `zoneId`.

2. **Periodic Data Flow**:

    - Firmware publishes to `readData/periodic/res` connector subscribes on this topic for the data
    - Periodicity is configured through `firmware-config.json -> mqttTopics -> periodicityInterval`
    - \*we need `zoneId` with this data from firmware side.

        ```json
        {
            "timestamp": 1749548733000,
            "zoneId": "zone_a",
            "cards": [
                {
                    "cardId": "CD1",
                    "channels": [
                        {
                            "channelId": "CH1",
                            "channelName": "Unit Heater",
                            "value": 1,
                            "unit": "C",
                            "status": "success"
                        },
                        {
                            "channelId": "CH5",
                            "channelName": "Humidity",
                            "value": 5,
                            "unit": "%",
                            "status": "success"
                        }
                    ]
                }
            ]
        }
        ```

    - Program will **republish** transformed data to `argus/bangalore/aatmunnOffice/periodic` with configured QoS

    ```json
    {
        "timestamp": 1749548733000,
        "zones": [
            {
                "hubId": "hub1", // added from mapping
                "zoneId": "zone_a",
                "zoneName": "Zone A", // added from mapping
                "cards": [
                    {
                        "cardId": "CD1",
                        "cardName": "Card 1",
                        "channels": [
                            {
                                "channelId": "CH1",
                                "channelName": "Unit Heater",
                                "value": 1,
                                "unit": "C",
                                "status": "success"
                            },
                            {
                                "channelId": "CH5",
                                "channelName": "Humidity",
                                "value": 5,
                                "unit": "%",
                                "status": "success"
                            }
                        ]
                    }
                ]
            }
        ]
    }
    ```

3. **On-Demand Sensor Data Flow**:

    - Cloud Platform publishes to `argus/bangalore/aatmunnOffice/pullSensorData`:

        ```json
        {
            "requestType": "all/zone/card/channel",
            "value": "*/${zoneId}/${cardId}/${channel}"
        }
        ```

    - examples:

        1. **Get all data of all zones**

            - from server side on `argus/bangalore/aatmunnOffice/pullSensorData` topic program will receive payload as follows:

                ```json
                {
                    "requestType": "all",
                    "value": "*"
                }
                ```

            - program will republish same payload to `readData/req` topic for client

        2. **Get all card data of provided zone**

            - from server side on `argus/bangalore/aatmunnOffice/pullSensorData` topic program will receive payload as follows:

                ```json
                {
                    "requestType": "zone",
                    "hubId": "hub1",
                    "value": "zone_a"
                }
                ```

            - program will check if `hubId` in the request matches with the `hubId` in the mapping for the `zoneId` here value. If not, it ignores the request.
            - program will get all the cards for that zone and publish request to `readData/req` topic for client(new request type cards)
                ```json
                {
                    "requestType": "cards",
                    "values": ["CD1", "CD2", "CD3", "CD4", "CD5"]
                }
                ```

        3. **Get all channel data of provided card**

            - from server side on `argus/bangalore/aatmunnOffice/pullSensorData` topic program will receive data as follows:

                ```json
                {
                    "requestType": "card",
                    "hubId": "hub1",
                    "value": "zone_a/CD1"
                }
                ```

            - program will check if `hubId` in the request matches with the `hubId` in the mapping for the `zoneId` here value. If not, it ignores the request.
            - program will check if `cardId` in the request matches with the `cardId` in the mapping for the `zoneId` here value. If not, it ignores the request.
            - program will publish below payload to `readData/req` topic for client

                ```json
                {
                    "requestType": "card",
                    "value": "CD1"
                }
                ```

        4. **Get specific channel data**

            - from server side on `argus/bangalore/aatmunnOffice/pullSensorData` topic program will receive payload as follows:

                ```json
                {
                    "requestType": "channel",
                    "hubId": "hub1",
                    "value": "zone_a/CD1/CH1"
                }
                ```

            - program will check if `hubId` in the request matches with the `hubId` in the mapping for the `zoneId` here value. If not, it ignores the request.
            - program will check if `cardId` in the request matches with the `cardId` in the mapping for the `zoneId` here value. If not, it ignores the request.
            - program will publish below payload to `readData/req` topic for client

                ```json
                {
                    "requestType": "channel",
                    "value": "CD1/CH1"
                }
                ```

    - Program receives responses on `readData/res` from client

        - Depending on the request type, response will be different but it will be in same format as below

        ```json
        <!-- response data received on readData/res -->
        {
            "timestamp": 1716123200000,
            "zoneId": "zone_a",
            "cards": [
                {
                    "cardId": "CD1",
                    "channels": [
                        {
                            "channelId": "CH1",
                            "channelName": "Unit Heater",
                            "value": 1,
                            "unit": "C",
                            "status": "success"
                        },
                        {
                            "channelId": "CH5",
                            "channelName": "Humidity",
                            "value": 5,
                            "unit": "%",
                            "status": "success"
                        }
                    ]
                },
                {
                    "cardId": "CD7",
                    "channels": [
                        {
                            "channelId": "CH1",
                            "channelName": "Unit Heater",
                            "value": 1,
                            "unit": "C",
                            "status": "success"
                        },
                        {
                            "channelId": "CH5",
                            "channelName": "Humidity",
                            "value": 5,
                            "unit": "%",
                            "status": "success"
                        }
                    ]
                }
            ]
        }
        ```

        - For `all`, includes all zones.
        - For `zone` requests, program filters the response to include only the specified `zoneId` and send only that `zoneId` data on `readData/res` topic.
        - from configured mapping resultant message will include zoneName and hubId.
        - Program publishes the **filtered/transformed response** to `argus/bangalore/aatmunnOffice/periodic` with configured QoS.

4. **Control Message Flow**:

    - \*We need `channelName` in request
    - Cloud Platform publishes to `argus/bangalore/aatmunnOffice/reqControlData`:

        ```json
        {
            "hubId": "hub1",
            "zoneId": "zone_a",
            "cardId": "CD1",
            "channelId": "CH1",
            "channelName": "Unit Heater",
            "value": 0
        }
        ```

    - Program checks if given `cardId` belongs to the requested `zoneId`. If not, it ignores the request.
    - If yes, it checks if `zoneId` belongs to the requested `hubId`. If not, it ignores the request.
    - Program publishes to `writeData/req` with configured QoS:

        ```json
        {
            "cards": [
                {
                    "cardId": "CD1",
                    "channels": [
                        {
                            "channelId": "CH1",
                            "channelName": "Unit Heater",
                            "value": 1,
                            "unit": "c"
                        }
                    ]
                }
            ]
        }
        ```

    - Program get response on `writeData/res`

    ```json
    {
        "timestamp": 1716123200000,
        "zoneId": "zone_a",
        "cards": [
            {
                "cardId": "CD1",
                "channels": [
                    {
                        "channelId": "CH1",
                        "channelName": "Unit Heater",
                        "value": 0,
                        "unit": "%",
                        "status": "success"
                    }
                ]
            }
        ]
    }
    ```

    - and republishes to `argus/bangalore/aatmunnOffice/responseControlData` with configured QoS:

        ```json
        {
            "timestamp": 1716123200000,
            "zones": [
                {
                    "hubId": "hub1",
                    "zoneId": "zone_a",
                    "zoneName": "Zone A",
                    "cards": [
                        {
                            "cardId": "CD1",
                            "cardName": "CD1",
                            "channels": [
                                {
                                    "channelId": "CH1",
                                    "channelName": "Unit Heater",
                                    "value": 0,
                                    "unit": "%",
                                    "status": "success"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        ```

## Assumptions

-   client topics are configured in `configs/config-client.json`
-   server topics are configured in `configs/config-server.json`
-   firmware config is in `configs/firmware-config.json`
-   mapping config is in `configs/mapping-config.json`
-   all config files should be in the `configs/` directory relative to where the program is executed

## Configuration

### example config.json

```json
{
    "mqtt": {
        "broker": "broker.example.com",
        "port": 8883,
        "username": "user",
        "password": "pass",
        "caCert": "/path/to/ca.crt",
        "clientCert": "/path/to/client.crt",
        "clientKey": "/path/to/client.key",
        "clientId": "go-mqtt-client"
    },
    "topics": {
        "subscribe": ["subscribeTopic1", "subscribeTopic2"],
        "publish": ["publishTopic1", "publishTopic2"]
    }
}
```
