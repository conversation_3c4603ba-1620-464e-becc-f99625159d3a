# API Interface (MQTT)

This document describes the MQTT topic and message format for the client(IO Firmware). The configuration for client topics is defined in `configs/config-client.json`.

### Read all sensors

From control program we will send req to :- **readData/req**
Control program will expect response on :- **readData/res**
QoS: 2 (Configurable)

#### Request

```json
{
    "requestType": "all / zone / card / channel",
    "value": "* / ${zoneId} / ${cardId} / ${channel}"
}
```

Examples:

1. Get all data:

```json
{
    "requestType": "all",
    "value": "*"
}
```

2. Get zone data: Instead of `zone` we are using **new request type** `cards`

```json
{
    "requestType": "cards",
    "values": ["CD1", "CD2", "CD3", "CD4", "CD5"]
}
```

3. Get card data:

```json
{
    "requestType": "card",
    "value": "CD1"
}
```

4. Get channel data:

```json
{
    "requestType": "channel",
    "value": "CD1/CH1"
}
```

#### Response

##### Success response

```json
// success response on - readData/res
{
    "timestamp": 1716123200000,
    "zoneId": "zone_a",
    "cards": [
        {
            "cardId": "CD1",
            "channels": [
                {
                    "channelId": "CH1",
                    "channelName": "Unit Heater",
                    "value": 1,
                    "unit": "C",
                    "status": "success"
                },
                {
                    "channelId": "CH5",
                    "channelName": "Humidity",
                    "value": 5,
                    "unit": "%",
                    "status": "success"
                }
            ]
        },
        {
            "cardId": "CD7",
            "channels": [
                {
                    "channelId": "CH1",
                    "channelName": "Unit Heater",
                    "value": 1,
                    "unit": "C",
                    "status": "success"
                },
                {
                    "channelId": "CH5",
                    "channelName": "Humidity",
                    "value": 5,
                    "unit": "%",
                    "status": "success"
                }
            ]
        }
    ]
}
```

##### Failure response

```json
// failure response on - readData/res
{
    "timestamp": 1716123200000,
    "zoneId": "zone_a",
    "cards": [
        {
            "cardId": "CD1",
            "channels": [
                {
                    "channelId": "CH1",
                    "channelName": "Unit Heater",
                    "status": "failure"
                },
                {
                    "channelId": "CH5",
                    "channelName": "Humidity",
                    "status": "failure"
                }
            ]
        },
        {
            "cardId": "CD7",
            "channels": [
                {
                    "channelId": "CH1",
                    "channelName": "Unit Heater",
                    "status": "failure"
                },
                {
                    "channelId": "CH5",
                    "channelName": "Humidity",
                    "status": "failure"
                }
            ]
        }
    ]
}
```

### Write to OP Device

From control program we will send req to :- **writeData/req**
Control program will expect response on :- **writeData/res**
QoS: 2 (Configurable)

#### Request

```json
{
    "cards": [
        {
            "cardId": "CD1",
            "channels": [
                {
                    "channelId": "CH1",
                    "channelName": "Unit Heater",
                    "value": 0,
                    "unit": "%"
                }
            ]
        }
    ]
}
```

#### Response

##### Success Response

```json
// success response on - writeData/res
{
    "timestamp": 1716123200000,
    "zoneId": "zone_a",
    "cards": [
        {
            "cardId": "CD1",
            "channels": [
                {
                    "channelId": "CH1",
                    "channelName": "Unit Heater",
                    "value": 0,
                    "unit": "%",
                    "status": "success"
                }
            ]
        }
    ]
}
```

##### Failure Response

```json
// failure response on - writeData/res
{
    "timestamp": 1716123200000,
    "zoneId": "zone_a",
    "cards": [
        {
            "cardId": "CD1",
            "channels": [
                {
                    "channelId": "CH1",
                    "channelName": "Unit Heater",
                    "status": "failure"
                }
            ]
        }
    ]
}
```

### Continuous Periodic read data from sensors

Control program will expect continuous periodic data on topic - **readData/periodic/res**
QoS: 2 (Configurable)

#### Response

```json
{
    "timestamp": 1716123200000,
    "zoneId": "zone_a",
    "cards": [
        {
            "cardId": "CD1",
            "channels": [
                {
                    "channelId": "CH1",
                    "channelName": "Unit Heater",
                    "value": 1,
                    "unit": "C",
                    "status": "success"
                },
                {
                    "channelId": "CH5",
                    "channelName": "Humidity",
                    "value": 5,
                    "unit": "%",
                    "status": "success"
                }
            ]
        }
    ]
}
```

## Request Flow from Server to Client

When the program receives pullSensorData requests from the cloud platform (server-communication.md), it validates and transforms them before sending to the IO Firmware.

All requests are validated against the mapping configuration before being forwarded to the client. Invalid requests are ignored.

Note: The program acts as a bridge between the Cloud Platform and IO Firmware, routing messages between the two configurations defined in `configs/config-server.json` and `configs/config-client.json`. The periodicity interval is configured through `configs/firmware-config.json -> mqttTopics -> periodicityInterval`.
